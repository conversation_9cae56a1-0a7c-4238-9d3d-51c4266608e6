{"navbar": {"reports": "Reports", "settings": "Settings", "ports": "Ports", "myPorts": "My Ports", "arrivalTracking": "Arrival Tracking", "dashboard": "Dashboard", "trips": "Trips", "monitoring": "Monitoring", "language": "Language", "logout": "Logout", "username": "Username"}, "reports": {"trips": "Trips", "alerts": "<PERSON><PERSON><PERSON>", "newAlerts": "New Alerts", "records": "Records", "statistics": "Statistics", "stops": "Stops", "employees": "Employees", "portDistribution": "Port Distribution", "tripTracking": "Trip Tracking", "activeTrips": "Active Trips"}, "common": {"chart": "Chart", "cancel": "Cancel", "alertId": "<PERSON><PERSON>", "alertType": "Alert <PERSON>", "transitNumber": "Transit Number", "routeName": "Route Name", "timestamp": "Timestamp", "address": "Address", "alertStatus": "Alert <PERSON>", "shipmentDescription": "Shipment Description", "details": "Details", "tripDetails": "Trip Details", "alertsDetails": "<PERSON><PERSON><PERSON>", "portIn": "Port In", "portOut": "Port Out", "latestInformationAboutTheTarget": "Latest Information About The Target", "tracker": "Tracker", "driver": "Driver", "truck": "Truck", "warnings": "Warnings", "status": "Status", "loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "send": "Send", "search": "Search", "filter": "Filter", "routes": "Routes", "justMyRoutes": "Just My Routes", "showAll": "Show All", "showPorts": "Show Ports", "showCheckPoint": "Show Check Point", "measureDistance": "Measure Distance", "clear": "Clear", "viewDetails": "View Details", "downloadTripPanelAsXLS": "Download Trip Panel as <PERSON><PERSON>", "back": "Back", "pageNotFound": "Page Not Found", "activeTripsWithAlerts": "Active Trips With Al<PERSON>s", "activeTripsWithoutAlerts": "Active Trips Without Alerts", "totalActiveTrips": "Total Active Trips", "totalClosedTrips": "Total Closed Trips", "closedTrips": "Closed Trips", "viewedTrips": "Viewed Trips", "notViewedTrips": "Not Viewed Trips", "tripCode": "Trip Code", "transitSequenceNo": "Transit Sequence No", "owner": "Owner", "elocks": "Elocks", "vehicleDetails": "Vehicle Details", "completeDistance": "Complete Distance", "remainingDistance": "Remaining Distance", "driverInfo": "Driver Info", "expectedArrivalDate": "Expected Arrival Date", "endDate": "End Date", "securityNotes": "Security Notes", "noDataExist": "No Data Exist", "tripMap": "Trip Map", "tripWarnings": "Trip Warnings", "pings": "Pings", "tripPings": "<PERSON>", "movementReport": "Movement Report", "activitiesReport": "Activities Report", "eventsReport": "Events Report", "tripViewer": "<PERSON>er", "alertsViewer": "<PERSON><PERSON><PERSON>", "tripAlerts": "<PERSON>", "location": "Location", "speed": "Speed", "noCharging": "No Charging", "batteryLife": "Battery Life {{ value }}", "close": "Close", "display_settings_button": "Display Settings", "tripFilters": "<PERSON>s", "changesApplyRealTime": "Changes apply in real time"}, "displaySettingsTab": {"displaySettings": "Display Settings", "tripDisplayMode": "Trip Display Mode", "clusterTrips": "Cluster Nearby Trips", "individualTrips": "Display Individual Trips", "checkpoints": "Checkpoints Display", "customsCheckpoints": "Customs Checkpoints", "policeCheckpoints": "Police Checkpoints", "suspiciousGeofences": "Suspicious Geofences", "ports": "Ports Display", "landPorts": "Land Ports", "seaports": "Seaports", "airports": "Airports", "changesApplyRealTime": "Changes apply in real-time"}, "ports": {"kingAbdulAzizSeaport": "King <PERSON><PERSON><PERSON><PERSON>port", "kingFahadIntlAirport": "King <PERSON><PERSON><PERSON>tl Airport", "jazanPort": "Jazan Port", "yanbuCommercialSeaport": "Yanbu Commercial Seaport", "ruqaieBorder": "Ruqaie Border", "jeddahIslamicSeaport": "Jeddah Islamic Seaport", "bathaCustomsBorder": "Batha Customs Border", "salwaCustomsBorder": "Salwa Customs Border", "khafjiBorder": "Khafji Border", "halatAmmarCustoms": "Halat Ammar Customs", "durrahCustomsBorder": "Durrah Customs Border", "dibaSeaportCustoms": "Diba Seaport Customs", "tuwalCustomsBorder": "Tuwal Customs Border", "wadiyahCustomsBorder": "Wadiyah Customs Border", "albCustomsBorder": "Alb Customs Border", "khadraCustomsBorder": "Khadra Customs Border", "kingFahadCauseway": "King <PERSON><PERSON><PERSON>", "ararCustomsBorder": "Arar Customs Border", "hadithaCustomsBorder": "Haditha Customs Border", "kingAbdulAzizAirport": "King AbdulAziz Airport", "kingAbdullahPortCustom": "King Abdullah Port Custom", "kingKhalidInternationalPort": "King <PERSON> International Port", "kingFahdIndustrialPortYanbu": "King Fahd Industrial Port in Yanbu", "madinaCustomsBorder": "Madina Customs Border", "riyadhDryPort": "Riyadh Dry Port", "emptyQuarterCustoms": "Empty Quarter Customs", "princeNaifBinAbdulazizIntlAirport": "Prince <PERSON><PERSON> Intl Airport", "kingFahdIndustrialPortDepositoryRedSea": "King Fahd Industrial Port depository area of the Red Sea", "kingFahadCommercialPortJubail": "King Fahad Commercial Port Jubail", "unipartKingKhaledAirport": "Unipart King Khaled Airport", "binZagrKingAbdullahPort": "BinZagr King Abdullah port", "sabicNonActivatedArea": "SABIC (non-activated area)", "nahdiMedicalCompany": "Nahdi Medical Company", "smsaCompany": "smsa company", "internationalMaritimeIndustries": "International Maritime Industries", "princeAbdulMohsinBinAbdulaziz": "Prince <PERSON>", "kingSalmanEnergyPark": "king salman energy park", "hailAirport": "Hail Airport", "taifAirport": "Taif Airport", "princeSultanBinAbdulazizAirportTabuk": "Prince <PERSON> bin Abdulaziz Airport - Tabuk", "eastrenGateDepositaryArea": "Eastren Gate Depositary Area", "bahriLogisticsDepositaryArea": "Bahri Logistics Depositary Area", "abhaAirport": "Abha Airport", "alAhsaAirport": "Al-Ahsa Airport"}, "tooltips": {"email": "Email"}, "filter": {"warnings": "Warnings", "truckInfo": "Truck Info", "transitNumber": "Transit Number", "transitSequenceNumber": "Transit Sequence Number", "driverName": "Driver Name", "plateNumber": "Plate Number", "trackerNumber": "Tracker Number", "tripCode": "Trip Code", "tripPriority": "Trip Priority", "high": "High", "medium": "Medium", "low": "Low", "tripStatus": "Trip Status", "active": "Active", "ended": "Ended", "tripLocation": "Trip Location", "onRoute": "On Route", "inExitBorder": "In Exit Border", "inEntryBorder": "In Entry Border", "date": "Date", "transactionDate": "Transaction Date", "startDate": "Start Date", "endDate": "End Date", "orderBy": "Order By", "descending": "Descending", "ascending": "Ascending", "entryPort": "Entry Port", "exitPort": "Exit Port", "transitNo": "Transit No", "transitDate": "Transit Date", "entryDate": "Entry Date", "exitDate": "Exit Date", "createdDate": "Created Date", "reset": "Reset", "applyFilter": "Apply Filter", "scope": "Scope Selection", "useFilters": "Use Filters", "allTrips": "All Trips", "tripCategory": "Trip Category", "myRoutes": "My Routes", "suspiciousTrips": "Suspicious Trips", "focusedTrips": "Focused Trips", "stoppedTrips": "Stopped Trips", "insideEntryPort": "Inside Entry Port", "inExitPort": "In Exit Port"}, "pages": {"monitorBoard": "Monitor Board", "ports": "Ports", "login": "<PERSON><PERSON>", "notFound": "Page Not Found"}, "login": {"welcomeBack": "Welcome Back", "login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>"}, "tripDetails": {"transitNumber": "Transit Number", "transitType": "Transit Type", "declarationDate": "Declaration Date", "transitSeqNo": "Transit Seq No", "ownerDescription": "Owner Description", "shipmentDescription": "Shipment Description", "vehicleDetails": "Vehicle Details", "entryPort": "Entry Port", "exitPort": "Exit Port", "startingDate": "Starting Date", "expectedArrivalDate": "Expected Arrival Date", "endDate": "End Date", "trackerNo": "Tracker No", "elocks": "Elocks", "driverName": "Driver Name", "driverPassportNumber": "Driver Passport Number", "driverNationality": "Driver Nationality", "driverContactNo": "Driver Contact No", "securityNotes": "Security Notes", "completeDistance": "Complete Distance", "remainingDistance": "Remaining Distance", "addSecurityNotes": "Add Security Notes", "endingTrip": "Ending Trip", "tripTracking": "Trip Tracking", "status": "Status"}, "emailDialog": {"title": "Send Email", "email": "Email", "recipientEmail": "Recipient Email Address", "subject": "Subject", "subjectPlaceholder": "Email Subject", "descriptionPlaceholder": "Email Description"}, "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "subjectRequired": "Subject is required", "descriptionRequired": "Description is required"}, "alert": {"name": "Name", "timeStamp": "Time Stamp", "status": "Status", "tripId": "Trip Id"}}