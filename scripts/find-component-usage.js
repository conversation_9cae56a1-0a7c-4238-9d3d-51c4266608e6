#!/usr/bin/env node

/**
 * Component Usage Finder
 * 
 * This script helps find where components are used throughout the project.
 * Usage: node scripts/find-component-usage.js <componentName>
 * Example: node scripts/find-component-usage.js Card
 */

const fs = require('fs');
const path = require('path');

function findComponentUsage(componentName, searchDir = 'src') {
  const results = [];
  
  function searchInFile(filePath, content) {
    const lines = content.split('\n');
    const imports = [];
    const usages = [];
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Find import statements
      if (line.includes('import') && line.includes(componentName)) {
        imports.push({
          line: lineNumber,
          content: line.trim(),
          type: 'import'
        });
      }
      
      // Find component usage (JSX)
      if (line.includes(`<${componentName}`) || line.includes(`</${componentName}`)) {
        usages.push({
          line: lineNumber,
          content: line.trim(),
          type: 'usage'
        });
      }
    });
    
    if (imports.length > 0 || usages.length > 0) {
      results.push({
        file: filePath,
        imports,
        usages,
        totalUsages: usages.length
      });
    }
  }
  
  function walkDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        walkDirectory(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          searchInFile(filePath, content);
        } catch (error) {
          console.warn(`Could not read file: ${filePath}`);
        }
      }
    });
  }
  
  walkDirectory(searchDir);
  return results;
}

function generateReport(componentName, results) {
  console.log(`\n📊 Component Usage Report for: ${componentName}`);
  console.log('='.repeat(50));
  
  if (results.length === 0) {
    console.log('❌ No usage found for this component.');
    return;
  }
  
  console.log(`✅ Found ${results.length} files using this component\n`);
  
  // Summary
  const totalUsages = results.reduce((sum, result) => sum + result.totalUsages, 0);
  console.log(`📈 Total usage count: ${totalUsages} instances\n`);
  
  // Detailed results
  results.forEach((result, index) => {
    console.log(`${index + 1}. 📁 ${result.file}`);
    
    if (result.imports.length > 0) {
      console.log('   📥 Imports:');
      result.imports.forEach(imp => {
        console.log(`      Line ${imp.line}: ${imp.content}`);
      });
    }
    
    if (result.usages.length > 0) {
      console.log(`   🔧 Usage (${result.usages.length} instances):`);
      result.usages.slice(0, 3).forEach(usage => { // Show first 3 usages
        console.log(`      Line ${usage.line}: ${usage.content}`);
      });
      
      if (result.usages.length > 3) {
        console.log(`      ... and ${result.usages.length - 3} more`);
      }
    }
    
    console.log('');
  });
  
  // Generate Storybook documentation snippet
  console.log('📝 Storybook Documentation Snippet:');
  console.log('-'.repeat(40));
  console.log('```markdown');
  console.log(`## Usage in Project\n`);
  console.log(`The ${componentName} component is used in ${results.length} files:\n`);
  
  results.forEach((result, index) => {
    console.log(`### ${index + 1}. ${path.basename(result.file, path.extname(result.file))}`);
    console.log(`**File:** \`${result.file}\``);
    console.log(`- Usage count: ${result.totalUsages} instances`);
    if (result.imports.length > 0) {
      console.log(`- Import: \`${result.imports[0].content}\``);
    }
    console.log('');
  });
  
  console.log('```');
}

// Main execution
const componentName = process.argv[2];

if (!componentName) {
  console.log('❌ Please provide a component name');
  console.log('Usage: node scripts/find-component-usage.js <componentName>');
  console.log('Example: node scripts/find-component-usage.js Card');
  process.exit(1);
}

console.log(`🔍 Searching for component: ${componentName}`);

const results = findComponentUsage(componentName);
generateReport(componentName, results);

// Export for programmatic use
module.exports = { findComponentUsage, generateReport };
