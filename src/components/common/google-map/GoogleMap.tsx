// src/components/common/google-map/GoogleMap.tsx
import { APIProvider, Map, Marker, useMap } from '@vis.gl/react-google-maps';
import React, { useEffect, useState, useRef } from 'react';
import { appConfig } from '@/shared/config/appSettings';

export type GoogleMapProps = {
  height?: string;
  width?: string;
  points?: any[];
  tripLocations?: any[];
};

function RulerOverlay({ start, end }: { start: google.maps.LatLngLiteral; end: google.maps.LatLngLiteral }) {
  const map = useMap();
  const overlayRef = useRef<google.maps.OverlayView>();

  useEffect(() => {
    if (!map || !start || !end) return;

    class Ruler extends google.maps.OverlayView {
      div: HTMLDivElement;

      constructor() {
        super();
        this.div = document.createElement('div');
      }

      onAdd() {
        this.getPanes()?.overlayMouseTarget.appendChild(this.div);
      }

      draw() {
        const projection = this.getProjection();
        if (!projection) return;

        const startPx = projection.fromLatLngToDivPixel(new google.maps.LatLng(start));
        const endPx = projection.fromLatLngToDivPixel(new google.maps.LatLng(end));

        const distance = google.maps.geometry.spherical.computeDistanceBetween(
          new google.maps.LatLng(start),
          new google.maps.LatLng(end)
        );
        const label = distance >= 1000
          ? `${(distance / 1000).toFixed(2)} km`
          : `${Math.round(distance)} m`;

        const midPx = {
          x: (startPx.x + endPx.x) / 2,
          y: (startPx.y + endPx.y) / 2,
        };
        const angle = Math.atan2(endPx.y - startPx.y, endPx.x - startPx.x) * (180 / Math.PI);

        const numTicks = 5;
        const ticks = Array.from({ length: numTicks - 1 }, (_, i) => {
          const t = (i + 1) / numTicks;
          return {
            x: startPx.x + (endPx.x - startPx.x) * t,
            y: startPx.y + (endPx.y - startPx.y) * t,
          };
        });

        const html = `
          <svg style="overflow: visible; position: absolute; left: 0; top: 0;">
            <line
              x1="${startPx.x}" y1="${startPx.y}"
              x2="${endPx.x}"   y2="${endPx.y}"
              stroke="black" stroke-width="2"
            />
            ${ticks.map(tick => `
              <line
                x1="${tick.x}" y1="${tick.y}"
                x2="${tick.x + 10 * Math.cos((angle+90)*Math.PI/180)}"
                y2="${tick.y + 10 * Math.sin((angle+90)*Math.PI/180)}"
                stroke="black" stroke-width="1"
              />
            `).join('')}
            <text
              x="${midPx.x}" y="${midPx.y - 10}"
              font-size="14px" font-weight="600"
              text-anchor="middle" fill="black">
              ${label}
            </text>
          </svg>
        `;
        this.div.innerHTML = html;
      }

      onRemove() {
        if (this.div.parentNode) this.div.parentNode.removeChild(this.div);
      }
    }

    const overlay = new Ruler();
    overlay.setMap(map);
    overlayRef.current = overlay;
    return () => overlay.setMap(null);
  }, [map, start, end]);

  return null;
}

export function GoogleMap({ height = '100%', width = '100%', points = [], tripLocations = [] }: GoogleMapProps) {
  const [rulerPoints, setRulerPoints] = useState<google.maps.LatLngLiteral[]>([]);

  const onClick = (e: google.maps.MapMouseEvent) => {
    if (!e.latLng) return;
    setRulerPoints(prev => prev.length === 2 ? [{ lat: e.latLng.lat(), lng: e.latLng.lng() }] : [...prev, { lat: e.latLng.lat(), lng: e.latLng.lng() }]);
  };

  return (
    <div style={{ height, width, position: 'relative' }}>
      <APIProvider apiKey={import.meta.env.VITE_TTS_GOOGLE_MAPS_API_KEY} libraries={['geometry']}>
        <Map
          center={appConfig.get('googleDefaultMapCenter')}
          zoom={appConfig.get('googleDefaultMapZoom')}
          style={{ height: '100%', width: '100%' }}
          onClick={onClick}
        >
          {/* Example markers */}
          {points.map(p => <Marker key={p.Id} position={{ lat: p.Lat, lng: p.Long }} />)}
          {tripLocations.map(t => <Marker key={t.id} position={{ lat: t.current_location.lat, lng: t.current_location.long }} />)}

          {rulerPoints.length === 2 && <RulerOverlay start={rulerPoints[0]} end={rulerPoints[1]} />}
        </Map>
      </APIProvider>
    </div>
  );
}
