:root {
    --accordion-radius: 26px;
}

.filter-container .accordion-header a {
    padding: 12px 1rem;
    font-size: 15px;
    border-color: transparent;
    box-shadow: 0px -3px 10px #33333315 inset;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.filter-container .accordion-header a .p-accordion-toggle-icon {
    display: none;
}

.filter-container .accordion-body .p-accordion-content {
    padding: 10px;
    border-radius: 0 0 var(--accordion-radius) var(--accordion-radius);
    background: var(--light-color);
}

.filter-container label {
    transition: 0.2s;
    cursor: pointer;
}

.filter-container label:not(:has(input:checked)) {
    color: #888;
    border: 1px solid transparent;
}

.filter-container label:has(input:checked) {
    font-weight: 500;
    color: var(--main-font-color);
    border: 1px solid #3300ff4f;
}
