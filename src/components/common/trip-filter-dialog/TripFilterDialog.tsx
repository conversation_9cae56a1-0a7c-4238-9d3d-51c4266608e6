// src/components/common/trip-filter-dialog/TripFilterDialog.tsx
/**
 * TripFilterDialog component
 * --------------------------
 * - Renders UI for advanced filters (ports, warnings, truck, dates, sorting).
 * - Uses `useTripFilterDialogStore` for shared filter state.
 * - Keeps small UI-only state (activeIndex for Accordion) local to component
 *   because it is ephemeral and not needed globally.
 *
 * Note: if you want to preserve the open/expanded tabs between navigations or
 * share them across components, we can lift `activeIndex` into the store later.
 */

import './TripFilterDialog.css';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { useState } from 'react';
import { IoArrowDownOutline, IoFilter, IoWarningSharp } from 'react-icons/io5';
import { MdFilterListAlt } from 'react-icons/md';
import { RiArrowLeftUpLine, RiResetLeftLine } from 'react-icons/ri';
import { FaTruck } from 'react-icons/fa';
import { FaCalendarDays } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import { Divider } from 'primereact/divider';

import { DatePicker } from '@/components/common/ui/DatePicker';
import { Button } from '@/components/common/ui/Button';
import { Input } from '@/components/common/ui/Input';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { Radio } from '@/components/common/ui/Radio';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/common/ui/Dialog';

import type { FiltersShape } from './types';
import { useTripFilterDialogStore } from './tripFilterDialogStore';

interface LocalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    showPortIn?: boolean;
    showPortOut?: boolean;
    showWarnings?: boolean;
    showTruck?: boolean;
    showDate?: boolean;
    showSorting?: boolean;
    onApply?: (filters: FiltersShape) => void;
}

export default function TripFilterDialog({
    open,
    onOpenChange,
    showPortIn = true,
    showPortOut = true,
    showWarnings = true,
    showTruck = true,
    showDate = true,
    showSorting = true,
    onApply,
}: Readonly<LocalProps>) {
    const { t } = useTranslation();
    const { t: tWarnings } = useTranslation('warnings');

    // store selectors (fine-grained selectors to minimize re-renders)
    const filters = useTripFilterDialogStore((state) => state.filters);
    const toggleListItem = useTripFilterDialogStore((state) => state.toggleListItem);
    // const setPriority = useTripFilterDialogStore((state) => state.setPriority);
    const setCategory = useTripFilterDialogStore((state) => state.setCategory);
    const setStatus = useTripFilterDialogStore((state) => state.setStatus);
    const setLocation = useTripFilterDialogStore((state) => state.setLocation);
    const setTransactionDate = useTripFilterDialogStore((state) => state.setTransactionDate);
    const setStartDate = useTripFilterDialogStore((state) => state.setStartDate);
    const setEndDate = useTripFilterDialogStore((state) => state.setEndDate);
    const setSorting = useTripFilterDialogStore((state) => state.setSorting);
    const resetFilters = useTripFilterDialogStore((state) => state.resetFilters);
    const applyFilters = useTripFilterDialogStore((state) => state.applyFilters);

    // activeIndex is UI-only (which tabs are expanded). It is ephemeral and belongs to local UI state.
    // Keeping it local avoids unnecessary global state and simplifies store shape.
    const [activeIndex, setActiveIndex] = useState<number | number[]>([0, 1, 2, 3, 4, 5]);

    const handleApply = () => {
        onOpenChange(false);
        onApply?.(filters);
        applyFilters();
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="w-screen h-screen max-w-none max-h-none p-0 filter-container">
                <DialogHeader className="px-6 py-3 pb-0" dir="ltr">
                    <DialogTitle className="text-2xl text-blue-600 flex items-center gap-3">
                        <MdFilterListAlt /> {t('common.filter')}
                    </DialogTitle>
                </DialogHeader>

                <DialogDescription />

                <div className="overflow-y-auto px-3">
                    <Accordion
                        className="flex flex-wrap gap-3"
                        multiple
                        activeIndex={activeIndex}
                        onTabChange={(e) => setActiveIndex(e.index)}>
                        {/* Port In */}
                        {showPortIn && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        <IoArrowDownOutline className="text-[18px] text-blue-500" />
                                        {t('common.portIn')}
                                    </span>
                                }>
                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {filters.portsIn.map((item, i) => (
                                        <Checkbox
                                            key={item.nameKey}
                                            label={t(item.nameKey)}
                                            checked={item.isChecked}
                                            onChange={(v) => toggleListItem('portsIn', i, v)}
                                        />
                                    ))}
                                </div>
                            </AccordionTab>
                        )}

                        {/* Port Out */}
                        {showPortOut && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        <RiArrowLeftUpLine className="text-[18px] text-red-500" />
                                        {t('common.portOut')}
                                    </span>
                                }>
                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {filters.portsOut.map((item, i) => (
                                        <Checkbox
                                            key={item.nameKey}
                                            label={t(item.nameKey)}
                                            checked={item.isChecked}
                                            onChange={(v) => toggleListItem('portsOut', i, v)}
                                        />
                                    ))}
                                </div>
                            </AccordionTab>
                        )}

                        {/* Warnings */}
                        {showWarnings && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        <IoWarningSharp className="text-[18px] text-red-500" />
                                        {t('filter.warnings')}
                                    </span>
                                }>
                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    <Radio label={tWarnings('activeWarnings')} name="warnings-radio-group" />
                                    <Radio
                                        label={tWarnings('allWarnings')}
                                        name="warnings-radio-group"
                                        defaultChecked
                                    />
                                </div>

                                <hr className="my-2" />

                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {filters.warnings.map((item, i) => (
                                        <Checkbox
                                            key={item.nameKey}
                                            label={tWarnings(item.nameKey)}
                                            checked={item.isChecked}
                                            onChange={(v) => toggleListItem('warnings', i, v)}
                                        />
                                    ))}
                                </div>
                            </AccordionTab>
                        )}

                        {/* Truck */}
                        {showTruck && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        <FaTruck className="text-[18px] text-green-700" />
                                        {t('filter.truckInfo')}
                                    </span>
                                }>
                                <div className="grid grid-cols-6 gap-x-5 gap-y-3">
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.transitNumber')}</label>
                                        <Input type="number" />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.transitSequenceNumber')}</label>
                                        <Input type="number" />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.driverName')}</label>
                                        <Input type="text" />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.plateNumber')}</label>
                                        <Input type="number" />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.trackerNumber')}</label>
                                        <Input type="number" />
                                    </div>
                                    <div className="text-[15px]">
                                        <label className="mb-1 block">{t('filter.tripCode')}</label>
                                        <Input type="text" />
                                    </div>
                                </div>

                                <div className="flex gap-4 mt-5">
                                    {/* <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block">{t('filter.tripPriority')}:</span>
                                        <div className="flex items-center justify-between gap-4 px-3">
                                            <Checkbox
                                                label={t('filter.high')}
                                                checked={filters.tripPriority.high}
                                                onChange={(v) => setPriority('high', v)}
                                            />
                                            <Checkbox]
                                                label={t('filter.medium')}
                                                checked={filters.tripPriority.medium}
                                                onChange={(v) => setPriority('medium', v)}
                                            />
                                            <Checkbox
                                                label={t('filter.low')}
                                                checked={filters.tripPriority.low}
                                                onChange={(v) => setPriority('low', v)}
                                            />
                                        </div>
                                    </div> */}
                                    <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block">{t('filter.tripCategory')}:</span>
                                        <div className="flex items-center justify-between gap-4 px-3">
                                            <Radio
                                                label={t('filter.myRoutes')}
                                                name="trip-category-radio-group"
                                                onChange={() => setCategory('myRoutes')}
                                            />
                                            <Radio
                                                label={t('filter.suspiciousTrips')}
                                                name="trip-category-radio-group"
                                                onChange={() => setCategory('suspiciousTrips')}
                                            />
                                            <Radio
                                                label={t('filter.focusedTrips')}
                                                name="trip-category-radio-group"
                                                onChange={() => setCategory('focusedTrips')}
                                            />
                                            <Radio
                                                label={t('filter.stoppedTrips')}
                                                name="trip-category-radio-group"
                                                onChange={() => setCategory('stoppedTrips')}
                                            />
                                        </div>
                                    </div>

                                    <Divider layout="vertical" />

                                    <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block">{t('filter.tripStatus')}:</span>
                                        <div className="flex items-center gap-4 px-3">
                                            <Checkbox
                                                label={t('filter.active')}
                                                checked={filters.tripStatus.active}
                                                onChange={(v) => setStatus('active', v)}
                                            />
                                            <Checkbox
                                                label={t('filter.ended')}
                                                checked={filters.tripStatus.ended}
                                                onChange={(v) => setStatus('ended', v)}
                                            />
                                        </div>
                                    </div>

                                    <Divider layout="vertical" />

                                    <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block">{t('filter.tripLocation')}:</span>
                                        <div className="flex items-center gap-4 px-3">
                                            <Checkbox
                                                label={t('filter.onRoute')}
                                                checked={filters.tripLocation.onRoute}
                                                onChange={(v) => setLocation('onRoute', v)}
                                            />
                                            <Checkbox
                                                label={t('filter.inExitBorder')}
                                                checked={filters.tripLocation.inExitBorder}
                                                onChange={(v) => setLocation('inExitBorder', v)}
                                            />
                                            <Checkbox
                                                label={t('filter.inEntryBorder')}
                                                checked={filters.tripLocation.inEntryBorder}
                                                onChange={(v) => setLocation('inEntryBorder', v)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </AccordionTab>
                        )}

                        {/* Date */}
                        {showDate && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[47.8vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        <FaCalendarDays className="text-[15px] text-gray-400" />
                                        {t('filter.date')}
                                    </span>
                                }>
                                <div className="text-[15px]">
                                    <label className="mb-1 block" htmlFor="transactionDate">
                                        {t('filter.transactionDate')}
                                    </label>
                                    <DatePicker
                                        value={filters.transactionDate}
                                        onChange={setTransactionDate}
                                        placeholder="dd/mm/yyyy"
                                        className="w-full"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-x-5 gap-y-1 mt-9 mb-3">
                                    <div className="text-[15px]">
                                        <label className="mb-1 block" htmlFor="startDate">
                                            {t('filter.startDate')}
                                        </label>
                                        <DatePicker
                                            value={filters.startDate}
                                            onChange={setStartDate}
                                            placeholder="dd/mm/yyyy"
                                        />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block" htmlFor="endDate">
                                            {t('filter.endDate')}
                                        </label>
                                        <DatePicker
                                            value={filters.endDate}
                                            onChange={setEndDate}
                                            placeholder="dd/mm/yyyy"
                                        />
                                    </div>
                                </div>
                            </AccordionTab>
                        )}

                        {/* Sorting */}
                        {showSorting && (
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[47.8vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        <IoFilter className="text-[18px] text-red-500" />
                                        {t('filter.orderBy')}
                                    </span>
                                }>
                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    <Radio
                                        label={t('filter.descending')}
                                        name="sorting-method"
                                        onChange={() => setSorting('desc')}
                                    />
                                    <Radio
                                        label={t('filter.ascending')}
                                        name="sorting-method"
                                        onChange={() => setSorting('asc')}
                                    />
                                </div>

                                <hr className="my-2" />

                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    <Radio
                                        label={t('filter.tripCode')}
                                        name="sorting-data"
                                        defaultChecked
                                        onChange={() => setSorting(filters.sorting.direction, 'tripCode')}
                                    />
                                    <Radio
                                        label={t('filter.transitNumber')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'transitNumber')}
                                    />
                                    <Radio
                                        label={t('filter.entryPort')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'entryPort')}
                                    />
                                    <Radio
                                        label={t('filter.exitPort')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'exitPort')}
                                    />
                                    <Radio
                                        label={t('filter.transitDate')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'transitDate')}
                                    />
                                    <Radio
                                        label={t('filter.entryDate')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'entryDate')}
                                    />
                                    <Radio
                                        label={t('filter.exitDate')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'exitDate')}
                                    />
                                    <Radio
                                        label={t('filter.createdDate')}
                                        name="sorting-data"
                                        onChange={() => setSorting(filters.sorting.direction, 'createdDate')}
                                    />
                                </div>
                            </AccordionTab>
                        )}
                    </Accordion>
                </div>

                <div className="ms-auto pb-4 px-10" dir="ltr">
                    <Button variant="outline" className="mx-3" onClick={() => resetFilters()}>
                        <RiResetLeftLine /> {t('filter.reset')}
                    </Button>
                    <Button onClick={handleApply}>
                        <MdFilterListAlt /> {t('filter.applyFilter')}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
