import type { NamedToggle } from './types';

export const basePortList: NamedToggle[] = [
    { nameKey: 'ports.kingFahadIntlAirport', isChecked: false },
    { nameKey: 'ports.jazanPort', isChecked: false },
    { nameKey: 'ports.yanbuCommercialSeaport', isChecked: false },
    { nameKey: 'ports.ruqaieBorder', isChecked: false },
    { nameKey: 'ports.jeddahIslamicSeaport', isChecked: false },
    { nameKey: 'ports.bathaCustomsBorder', isChecked: false },
    { nameKey: 'ports.salwaCustomsBorder', isChecked: false },
    { nameKey: 'ports.khafjiBorder', isChecked: false },
    { nameKey: 'ports.halatAmmarCustoms', isChecked: false },
    { nameKey: 'ports.durrahCustomsBorder', isChecked: false },
    { nameKey: 'ports.dibaSeaportCustoms', isChecked: false },
    { nameKey: 'ports.tuwalCustomsBorder', isChecked: false },
    { nameKey: 'ports.wadiyahCustomsBorder', isChecked: false },
    { nameKey: 'ports.albCustomsBorder', isChecked: false },
    { nameKey: 'ports.khadraCustomsBorder', isChecked: false },
    { nameKey: 'ports.kingFahadCauseway', isChecked: false },
    { nameKey: 'ports.ararCustomsBorder', isChecked: false },
    { nameKey: 'ports.hadithaCustomsBorder', isChecked: false },
    { nameKey: 'ports.kingAbdulAzizAirport', isChecked: false },
    { nameKey: 'ports.kingAbdullahPortCustom', isChecked: false },
    { nameKey: 'ports.madinaCustomsBorder', isChecked: false },
    { nameKey: 'ports.riyadhDryPort', isChecked: false },
    { nameKey: 'ports.emptyQuarterCustoms', isChecked: false },
    { nameKey: 'ports.unipartKingKhaledAirport', isChecked: false },
    { nameKey: 'ports.binZagrKingAbdullahPort', isChecked: false },
    { nameKey: 'ports.sabicNonActivatedArea', isChecked: false },
    { nameKey: 'ports.nahdiMedicalCompany', isChecked: false },
    { nameKey: 'ports.smsaCompany', isChecked: false },
    { nameKey: 'ports.internationalMaritimeIndustries', isChecked: false },
    { nameKey: 'ports.hailAirport', isChecked: false },
    { nameKey: 'ports.taifAirport', isChecked: false },
    { nameKey: 'ports.eastrenGateDepositaryArea', isChecked: false },
    { nameKey: 'ports.bahriLogisticsDepositaryArea', isChecked: false },
    { nameKey: 'ports.abhaAirport', isChecked: false },
    { nameKey: 'ports.alAhsaAirport', isChecked: false },
    { nameKey: 'ports.kingSalmanEnergyPark', isChecked: false },
    { nameKey: 'ports.kingKhalidInternationalPort', isChecked: false },
    { nameKey: 'ports.kingFahdIndustrialPortYanbu', isChecked: false },
    { nameKey: 'ports.kingFahadCommercialPortJubail', isChecked: false },
    { nameKey: 'ports.princeAbdulMohsinBinAbdulaziz', isChecked: false },
    { nameKey: 'ports.kingFahdIndustrialPortDepositoryRedSea', isChecked: false },
    { nameKey: 'ports.princeNaifBinAbdulazizIntlAirport', isChecked: false },
    { nameKey: 'ports.princeSultanBinAbdulazizAirportTabuk', isChecked: false },
];

export const baseWarningsList: NamedToggle[] = [
    { nameKey: 'acknowledge', isChecked: false },
    { nameKey: 'notAcknowledge', isChecked: false },
    { nameKey: 'trackerTamper', isChecked: false },
    { nameKey: 'trackerDropped', isChecked: false },
    { nameKey: 'lockTamper', isChecked: false },
    { nameKey: 'lockOpen', isChecked: false },
    { nameKey: 'lockConnectionLost', isChecked: false },
    { nameKey: 'trackerBatteryLow', isChecked: false },
    { nameKey: 'lockLowBattery', isChecked: false },
    { nameKey: 'lockVeryLowBattery', isChecked: false },
    { nameKey: 'gsmSignalLost', isChecked: false },
    { nameKey: 'gpsSignalLost', isChecked: false },
    { nameKey: 'geofenceEntryBreach', isChecked: false },
    { nameKey: 'geofenceExitBreach', isChecked: false },
    { nameKey: 'trackerConnectionLost', isChecked: false },
    { nameKey: 'tripDistanceExceeded', isChecked: false },
    { nameKey: 'tripTimeExceeded', isChecked: false },
    { nameKey: 'overSpeeding', isChecked: false },
    { nameKey: 'truckStopped', isChecked: false },
    { nameKey: 'wrongDirection', isChecked: false },
    { nameKey: 'entryIntoCustomsArea', isChecked: false },
    { nameKey: 'truckMoved', isChecked: false },
    { nameKey: 'fourHoursExceeded', isChecked: false },
    { nameKey: 'suspectedArea', isChecked: false },
];
