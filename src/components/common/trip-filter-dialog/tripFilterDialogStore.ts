// src/components/common/trip-filter-dialog/tripFilterDialogStore.ts
/**
 * tripFilterDialogStore
 * ---------------------
 * Zustand store for trip filters. Exports a hook-like store:
 *   useTripFilterDialogStore(selector)
 *
 * - Uses typed StateCreator to provide proper typings for set/get.
 * - Uses persist/devtools middlewares consistent with project style.
 */

import { create, type StateCreator } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys';
import { logger } from '@/infrastructure/logging';

import { basePortList, baseWarningsList } from './constants';
import type { TripFilterDialogStoreType, FiltersShape, TripFilterDialogState, ListSection } from './types';

const createDefaultFilters = (): FiltersShape => ({
    portsIn: basePortList.map((p) => ({ ...p })),
    portsOut: basePortList.map((p) => ({ ...p })),
    warnings: baseWarningsList.map((w) => ({ ...w })),
    tripPriority: { high: false, medium: false, low: false },
    tripCategory: 'myRoutes', // default category
    tripStatus: { active: false, ended: false },
    tripLocation: { onRoute: false, inExitBorder: false, inEntryBorder: false },
    transactionDate: null,
    startDate: null,
    endDate: null,
    sorting: { direction: 'desc', by: 'tripCode' },
});

// Typed StateCreator eliminates implicit any on set/get
const createTripFilterDialogState =
    (instanceId = 'trip-filter-dialog'): StateCreator<TripFilterDialogStoreType> =>
    (set, get) =>
        ({
            filters: createDefaultFilters(),

            setFilters: (updater) => {
                set((currentState: TripFilterDialogState) => {
                    const nextFilters =
                        typeof updater === 'function'
                            ? (updater as (prev: FiltersShape) => FiltersShape)(currentState.filters)
                            : { ...currentState.filters, ...updater };
                    return { filters: nextFilters };
                });
                logger.info('Trip filter dialog: filters updated', { instanceId });
            },

            toggleListItem: (section: ListSection, index: number, checked: boolean) => {
                set((currentState: TripFilterDialogState) => {
                    const listCopy = [...currentState.filters[section]];
                    if (index < 0 || index >= listCopy.length) {
                        return { filters: { ...currentState.filters } };
                    }
                    listCopy[index] = { ...listCopy[index], isChecked: checked };
                    return { filters: { ...currentState.filters, [section]: listCopy } };
                });
                logger.info('Trip filter dialog: toggle list item', { instanceId, section, index, checked });
            },

            setPriority: (priorityKey, value) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: {
                        ...currentState.filters,
                        tripPriority: { ...currentState.filters.tripPriority, [priorityKey]: value },
                    },
                }));
                logger.info('Trip filter dialog: set priority', { instanceId, priorityKey, value });
            },

            setCategory: (value) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: {
                        ...currentState.filters,
                        tripCategory: value,
                    },
                }));
                logger.info('Trip filter dialog: set category', { instanceId, value });
            },

            setStatus: (statusKey, value) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: {
                        ...currentState.filters,
                        tripStatus: { ...currentState.filters.tripStatus, [statusKey]: value },
                    },
                }));
                logger.info('Trip filter dialog: set status', { instanceId, statusKey, value });
            },

            setLocation: (locationKey, value) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: {
                        ...currentState.filters,
                        tripLocation: { ...currentState.filters.tripLocation, [locationKey]: value },
                    },
                }));
                logger.info('Trip filter dialog: set location', { instanceId, locationKey, value });
            },

            setTransactionDate: (date) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: { ...currentState.filters, transactionDate: date },
                }));
                logger.info('Trip filter dialog: set transaction date', { instanceId, date });
            },

            setStartDate: (date) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: { ...currentState.filters, startDate: date },
                }));
                logger.info('Trip filter dialog: set start date', { instanceId, date });
            },

            setEndDate: (date) => {
                set((currentState: TripFilterDialogState) => ({ filters: { ...currentState.filters, endDate: date } }));
                logger.info('Trip filter dialog: set end date', { instanceId, date });
            },

            setSorting: (direction, by) => {
                set((currentState: TripFilterDialogState) => ({
                    filters: {
                        ...currentState.filters,
                        sorting: { direction, by: by ?? currentState.filters.sorting.by },
                    },
                }));
                logger.info('Trip filter dialog: set sorting', { instanceId, direction, by });
            },

            resetFilters: () => {
                set({ filters: createDefaultFilters() });
                logger.info('Trip filter dialog: reset filters', { instanceId });
            },

            applyFilters: (callback) => {
                const current = get().filters;
                callback?.(current);
                logger.info('Trip filter dialog: apply filters', { instanceId, filters: current });
            },
        }) as TripFilterDialogStoreType;

// exported hook-like store (name follows your preference)
export const useTripFilterDialogStore = create<TripFilterDialogStoreType>()(
    subscribeWithSelector(
        devtools(
            persist(createTripFilterDialogState('trip-filter-dialog'), {
                name: LocalStorageKeys.tripFilterDialog, // ensure this key exists in your enum
            }),
            { name: 'trip-filter-dialog-store' },
        ),
    ),
);
