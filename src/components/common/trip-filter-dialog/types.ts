// src/components/common/trip-filter-dialog/types.ts
/**
 * Trip Filter Dialog - Shared Types
 * ---------------------------------
 * Centralized type definitions used by the TripFilterDialog component and the
 * tripFilterDialog store. Keeping types here avoids duplication and keeps
 * runtime code lighter.
 */

export type NamedToggle = {
    /** translation key (e.g. "ports.kingFahadIntlAirport") */
    nameKey: string;
    /** whether the toggle is currently checked */
    isChecked: boolean;
};

export type SortingShape = {
    direction: 'asc' | 'desc';
    by: string;
};

export type TripCategory = 'myRoutes' | 'suspiciousTrips' | 'focusedTrips' | 'stoppedTrips';

export type FiltersShape = {
    portsIn: NamedToggle[];
    portsOut: NamedToggle[];
    warnings: NamedToggle[];
    tripPriority: { high: boolean; medium: boolean; low: boolean };
    tripCategory: TripCategory;
    tripStatus: { active: boolean; ended: boolean };
    tripLocation: { onRoute: boolean; inExitBorder: boolean; inEntryBorder: boolean };
    transactionDate: Date | null;
    startDate: Date | null;
    endDate: Date | null;
    sorting: SortingShape;
};

/** sections that are list-based and share the NamedToggle shape */
export type ListSection = 'portsIn' | 'portsOut' | 'warnings';

/** Props accepted by the TripFilterDialog React component */
export interface TripFilterDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;

    showPortIn?: boolean;
    showPortOut?: boolean;
    showWarnings?: boolean;
    showTruck?: boolean;
    showDate?: boolean;
    showSorting?: boolean;

    /** Optional callback invoked when user applies filters */
    onApply?: (filters: FiltersShape) => void;
}

/** Store state (subset) */
export type TripFilterDialogState = {
    filters: FiltersShape;
};

/** Actions (store API) */
export type TripFilterDialogActions = {
    setFilters: (updater: Partial<FiltersShape> | ((prev: FiltersShape) => FiltersShape)) => void;
    toggleListItem: (section: ListSection, index: number, checked: boolean) => void;
    setPriority: (key: keyof FiltersShape['tripPriority'], value: boolean) => void;
    setCategory: (value: TripCategory) => void;
    setStatus: (key: keyof FiltersShape['tripStatus'], value: boolean) => void;
    setLocation: (key: keyof FiltersShape['tripLocation'], value: boolean) => void;
    setTransactionDate: (d: Date | null) => void;
    setStartDate: (d: Date | null) => void;
    setEndDate: (d: Date | null) => void;
    setSorting: (direction: SortingShape['direction'], by?: string) => void;
    resetFilters: () => void;
    applyFilters: (callback?: (f: FiltersShape) => void) => void;
};

/** Full store type */
export type TripFilterDialogStoreType = TripFilterDialogState & TripFilterDialogActions;
