import * as React from 'react';

/**
 * Props for the Checkbox component.
 * Extends native HTML input props (excluding `onChange` and `type`),
 * and adds a `label` for display and a custom `onChange` handler.
 */
interface CheckboxProps extends Omit<React.ComponentProps<'input'>, 'onChange' | 'type'> {
    /** The text label displayed next to the checkbox */
    label: string;

    /**
     * Optional callback triggered when the checkbox value changes.
     * Receives the new checked state (true or false).
     */
    onChange?: (checked: boolean) => void;
}

/**
 * A reusable, styled checkbox component with a label.
 * Supports forwarding refs and integrates easily into forms.
 *
 * Example usage with multiple items:
 *
 * ```tsx
 * const items = ['Option 1', 'Option 2', 'Option 3'];
 * const [selected, setSelected] = React.useState<string[]>([]);
 *
 * const handleCheckboxChange = (label: string, checked: boolean) => {
 *   setSelected(prev =>
 *     checked ? [...prev, label] : prev.filter(item => item !== label)
 *   );
 * };
 *
 * return (
 *   {items.map(item => (
 *     <Checkbox
 *       key={item}
 *       label={item}
 *       checked={selected.includes(item)}
 *       onChange={(checked) => handleCheckboxChange(item, checked)}
 *     />
 *   ))}
 * );
 * ```
 */
const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
    ({ label, onChange, className = '', ...props }, ref) => {
        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            onChange?.(e.target.checked);
        };

        return (
            <label
                htmlFor={props.id}
                className={`
          flex items-center gap-3 hover:bg-gray-100 py-1 px-2 rounded text-[14px]
          transition-all duration-200 cursor-pointer border
          ${props.checked ? 'border-blue-300' : 'border-transparent'}
          ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${className}
        `}>
                <input ref={ref} type="checkbox" onChange={handleChange} className="w-3 h-3" {...props} />
                <span className="flex-1">{label}</span>
            </label>
        );
    },
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
