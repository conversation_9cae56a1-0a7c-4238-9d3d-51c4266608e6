import * as React from 'react';

/**
 * Props for the Radio component.
 * Extends native HTML input props (excluding `onChange` and `type`),
 * and adds a `label` for display and a custom `onChange` handler.
 */
interface RadioProps extends Omit<React.ComponentProps<'input'>, 'onChange' | 'type'> {
    /** The text label displayed next to the radio button */
    label: string;

    /**
     * Optional callback triggered when the radio is selected.
     * Receives `true` when selected.
     */
    onChange?: (checked: boolean) => void;
}

/**
 * A reusable, styled radio button component with a label.
 *
 * Features:
 * - Fully styled with Tailwind CSS.
 * - Supports forwarding refs for integration with form libraries.
 * - Automatically generates a unique `id` if not provided, ensuring proper label association.
 * - Calls `onChange` only when the radio is selected.
 *
 * Usage Example: Single-choice group
 * ```tsx
 * const options = ['Option A', 'Option B', 'Option C'];
 * const [selected, setSelected] = React.useState<string>('');
 *
 * return (
 *   {options.map(option => (
 *     <Radio
 *       key={option}
 *       name="choices" // Use the same name for the group of radios
 *       label={option}
 *       checked={selected === option}
 *       onChange={() => setSelected(option)}
 *     />
 *   ))}
 * );'
 * ```
 */
const Radio = React.forwardRef<HTMLInputElement, RadioProps>(
    ({ label, onChange, className = '', id, ...props }, ref) => {
        // Always call useId to avoid conditional hook usage
        const generatedId = React.useId();
        const inputId = id ?? generatedId;

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            if (e.target.checked) {
                onChange?.(true);
            }
        };

        return (
            <label
                htmlFor={inputId}
                className={`
                    flex items-center gap-3 hover:bg-gray-100 py-1 px-2 rounded text-[14px]
                    transition-all duration-200 cursor-pointer border
                    ${props.checked ? 'border-blue-300' : 'border-transparent'}
                    ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                    ${className}
                `}>
                <input id={inputId} ref={ref} type="radio" onChange={handleChange} className="w-3 h-3" {...props} />
                <span className="flex-1">{label}</span>
            </label>
        );
    },
);

Radio.displayName = 'Radio';

export { Radio };
