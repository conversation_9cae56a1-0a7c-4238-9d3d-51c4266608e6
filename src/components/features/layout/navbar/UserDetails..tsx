import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { IoLogOutOutline } from 'react-icons/io5';

import userAvatar from '@imgs/avatar.png';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/common/ui/DropdownMenu';

export default function UserDetails() {
    const { t } = useTranslation();

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger className="outline-none">
                    <div className="flex items-center gap-3 ">
                        <p className="m-0 font-semibold text-[15px] whitespace-nowrap"> Username </p>
                        <img
                            className="inline-block size-6.5 rounded-full ring-2 ring-[#aaaaaa3d]"
                            src={userAvatar}
                            alt="User Avatar"
                        />
                    </div>
                </DropdownMenuTrigger>

                <DropdownMenuContent sideOffset={8} className="text-center _effect mx-4">
                    <Link to="/login" className="w-[100%] font-semibold ">
                        <DropdownMenuItem className="cursor-pointer w-[100%] text-[red] flex gap-3 items-center justify-center  hover:bg-gray-100">
                            <span className="text-[red] "> {t('navbar.logout')} </span>
                            <IoLogOutOutline className="size-[18px] text-[red]" />
                        </DropdownMenuItem>
                    </Link>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
}
