import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { HiOutlineMail } from 'react-icons/hi';
import { HiMiniLockClosed } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';

import { useLoginStore } from '@/stores/loginStore';
import { Button } from '@/components/common/ui/Button';
import Loader from '@/components/common/ui/Loader';

export default function LoginForm() {
    const [showPassword, setShowPassword] = useState<boolean>(false);
    const { t } = useTranslation();
    const navigate = useNavigate();
    const isDataLoading = useLoginStore((state) => state.isDataLoading);
    const setUsername = useLoginStore((state) => state.setUsername);
    const setPassword = useLoginStore((state) => state.setPassword);
    const submit = useLoginStore((state) => state.submit);

    return (
        <>
            <form className="login-form">
                <div className="flex items-center rounded-md bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-1 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-400">
                    <HiOutlineMail className="text-[#aaa] pointer-events-none col-start-1 row-start-1 mr-2 size-8 self-center justify-self-end  sm:size-4" />
                    <input
                        type="text"
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder={t('login.username')}
                        className="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
                    />
                </div>

                <div className="mt-5 flex transition items-center rounded-md bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-1 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-400">
                    <HiMiniLockClosed className="text-[#aaa] me-2.5" />
                    <input
                        type={showPassword ? 'text' : 'password'}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder={t('login.password')}
                        className="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
                    />
                    <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-[#aaa] hover:text-gray-600 transition-colors duration-200 mr-2 p-1"
                        aria-label={showPassword ? 'Hide password' : 'Show password'}>
                        {showPassword ? (
                            <FaEyeSlash className="size-8 self-center justify-self-end sm:size-4" />
                        ) : (
                            <FaEye className="size-8 self-center justify-self-end sm:size-4" />
                        )}
                    </button>
                </div>

                <Button
                    type="submit"
                    disabled={isDataLoading}
                    className="bg-[var(--blue-color)] text-[#f2f2f2] w-[100%] mt-5 login-button"
                    variant="outline"
                    onClick={(e) => {
                        e.preventDefault();
                        submit(navigate);
                    }}>
                    <Loader isLoading={isDataLoading} />
                    {t('login.loginButton')}
                </Button>
            </form>
        </>
    );
}
