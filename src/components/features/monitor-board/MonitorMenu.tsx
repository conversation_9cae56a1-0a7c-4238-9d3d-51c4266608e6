import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IoChevronUpOutline } from 'react-icons/io5';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/common/ui/Tabs';
import { Button } from '@/components/common/ui/Button';
import TripFilterDialog from '@/components/common/trip-filter-dialog/TripFilterDialog';

import RoutesTap from './menu-taps/RoutesTap';
import AlertsTap from './menu-taps/AlertsTap';
import DisplaySettingsTap from './menu-taps/display-settings-tap/DisplaySettingsTap';

export default function MonitorMenu() {
    const { t } = useTranslation();
    const [visible, setVisible] = useState(false);

    return (
        <Tabs className="me-3">
            <TabsList className="m-auto">
                <TabsTrigger value="DisplaySettings"> {t('common.display_settings_button')} </TabsTrigger>
                <TabsTrigger value="Search">
                    <Button size="sm" variant="null" className="w-full" onClick={() => setVisible(true)}>
                        {t('common.search')}
                    </Button>
                    <TripFilterDialog open={visible} onOpenChange={setVisible} showDate={false} showSorting={false} />
                </TabsTrigger>
                <TabsTrigger value="Alerts"> {t('reports.alerts')} </TabsTrigger>
                <TabsTrigger value="Routes"> {t('common.routes')} </TabsTrigger>
                <TabsTrigger value="close">
                    <IoChevronUpOutline className="size-3.2" />
                </TabsTrigger>
            </TabsList>
            <TabsContent value="DisplaySettings" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <DisplaySettingsTap />
            </TabsContent>
            <TabsContent value="Alerts" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <AlertsTap />
            </TabsContent>
            <TabsContent value="Routes" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <RoutesTap />
            </TabsContent>
        </Tabs>
    );
}
