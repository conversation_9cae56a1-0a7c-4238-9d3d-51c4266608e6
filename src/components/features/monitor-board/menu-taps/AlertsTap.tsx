import { useTranslation } from 'react-i18next';
import { FcAdvertising } from 'react-icons/fc';
import { Md<PERSON><PERSON><PERSON>, MdAdd, Md<PERSON><PERSON>erList } from 'react-icons/md';
import { BatteryLow, BatteryFull, PlugZap, Plug, MapPin, AlertTriangle, Gauge } from 'lucide-react';
import { useState } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/ui/Card';
import { useAlertStore } from '@/stores/alertStore';
import { Checkbox } from '@/components/common/ui/Checkbox';

export default function AlertsTap() {
    const { t, i18n } = useTranslation();
    const alertsList = useAlertStore((state) => state.alerts);
    const selectedAlertTypeIds = useAlertStore((state) => state.selectedAlertTypeIds || []);
    const setSelectedAlertTypeIds = useAlertStore((state) => state.setSelectedAlertTypeIds);

    const [isMinimized, setIsMinimized] = useState(false);
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    // Battery icon
    const getBatteryIcon = (batteryLevelPercentage: number) => {
        return batteryLevelPercentage < 20 ? (
            <BatteryLow className="w-4 h-4 text-red-500" />
        ) : (
            <BatteryFull className="w-4 h-4 text-green-500" />
        );
    };

    // Charger icon
    const getChargerIcon = (chargerStatus: number) => {
        return chargerStatus === 1 ? (
            <PlugZap className="w-4 h-4 text-green-500" />
        ) : (
            <Plug className="w-4 h-4 text-gray-400" />
        );
    };

    // Location icons
    const getLocationIcons = (isWithinRouteGeofence: boolean, isWithinSuspiciousZone: boolean) => {
        const icons: JSX.Element[] = [];
        icons.push(
            <MapPin
                key="geofence"
                className={`w-4 h-4 ${isWithinRouteGeofence ? 'text-green-500' : 'text-red-500'}`}
            />,
        );
        if (isWithinSuspiciousZone) {
            icons.push(<AlertTriangle key="suspicious" className="w-4 h-4 text-yellow-500" />);
        }
        return icons;
    };

    // Speed icon
    const getSpeedIcon = (currentSpeed: number) => {
        if (currentSpeed > 100) return <Gauge className="w-4 h-4 text-red-500" />; // Overspeed
        if (currentSpeed > 0) return <Gauge className="w-4 h-4 text-green-500" />; // Moving
        return <Gauge className="w-4 h-4 text-gray-400" />; // Stopped
    };

    // Filter logic
    const alertTypes = Array.from(new Map(alertsList.map((a) => [a.alertType.id, a.alertType])).values());

    const toggleSelection = (id: number) => {
        if (selectedAlertTypeIds.includes(id)) {
            setSelectedAlertTypeIds(selectedAlertTypeIds.filter((sid) => sid !== id));
        } else {
            setSelectedAlertTypeIds([...selectedAlertTypeIds, id]);
        }
    };

    const filteredAlerts = alertsList.filter(
        (alert) => selectedAlertTypeIds.length === 0 || selectedAlertTypeIds.includes(alert.alertType.id),
    );

    const [copiedTripId, setCopiedTripId] = useState<string | null>(null);

    return (
        <>
            {!isMinimized && (
                <Card className="_effect fixed left-2 top-15 m-4 w-100 shadow-lg" dir={i18n.dir()}>
                    {/* Header */}
                    <CardHeader className="px-4 grid grid-cols-12 items-center border-b bg-white">
                        <div className="col-span-3 flex justify-start">
                            <button
                                onClick={() => setIsMinimized(true)}
                                className="border border-gray-300 rounded p-1 text-gray-500 hover:text-gray-700">
                                <MdRemove size={20} />
                            </button>
                        </div>
                        <div className="col-span-6 flex justify-center">
                            <CardTitle className="flex items-center gap-2">
                                <FcAdvertising className="size-5" />
                                {t('reports.newAlerts')}
                            </CardTitle>
                        </div>
                        <div className="col-span-3 flex justify-end">
                            <button
                                onClick={() => setIsFilterOpen(!isFilterOpen)}
                                className="border border-gray-300 rounded p-1 text-gray-500 hover:text-gray-700">
                                <MdFilterList size={20} />
                            </button>
                            {isFilterOpen && (
                                <div
                                    dir={i18n.dir()}
                                    className={`absolute mt-2 w-56 top-15 bg-white border border-gray-200 rounded shadow-lg z-50 ${
                                        i18n.dir() === 'rtl' ? 'left-0' : 'right-0'
                                    }`}>
                                    <div className="p-3">
                                        <div className="space-y-2 max-h-60 overflow-auto">
                                            {alertTypes.map((type) => {
                                                const label =
                                                    i18n.language === 'ar' ? type.name.arabic : type.name.english;
                                                return (
                                                    <Checkbox
                                                        key={type.id}
                                                        label={label}
                                                        checked={selectedAlertTypeIds.includes(type.id)}
                                                        onChange={() => toggleSelection(type.id)}
                                                    />
                                                );
                                            })}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardHeader>

                    {/* Alerts list */}
                    <div className="max-h-[calc(93vh-200px)] overflow-y-auto">
                        <CardContent className="grid gap-2">
                            {filteredAlerts.slice(0, 20).map((alert, index) => (
                                <div key={index} className="alert-item">
                                    <div className="flex flex-col gap-2">
                                        {/* Row 1: Name + Time */}
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <p className="text-[15px] font-medium">
                                                    {i18n.language === 'ar'
                                                        ? alert.alertType.name.arabic
                                                        : alert.alertType.name.english}
                                                </p>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <small className="text-[#888] text-[14px]">{alert.timeStamp}</small>
                                            </div>
                                        </div>

                                        {/* Row 2: Trip ID + Status */}
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <small
                                                    className="text-blue-500 text-[14px] font-bold cursor-pointer relative"
                                                    onClick={() => {
                                                        navigator.clipboard.writeText(alert.tripId.toString());
                                                        setCopiedTripId(alert.tripId.toString());
                                                        setTimeout(() => setCopiedTripId(null), 1500);
                                                    }}>
                                                    {alert.tripId}

                                                    {/* Temporary tooltip */}
                                                    {copiedTripId === alert.tripId.toString() && (
                                                        <span className="absolute -top-6 left-1/2 -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
                                                            Copied!
                                                        </span>
                                                    )}

                                                    {/* Default tooltip on hover */}
                                                    {copiedTripId !== alert.tripId.toString() && (
                                                        <span className="absolute -top-6 left-1/2 -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity z-10">
                                                            trip code
                                                        </span>
                                                    )}
                                                </small>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <span className="flex items-center">
                                                    {getBatteryIcon(alert.currentStateOfTrip.batteryLevelPercentage)}
                                                    {getChargerIcon(alert.currentStateOfTrip.chargerStatus)}
                                                    {getLocationIcons(
                                                        alert.currentStateOfTrip.isWithinRouteGeofence,
                                                        alert.currentStateOfTrip.isWithinSuspiciousZone,
                                                    )}
                                                    {getSpeedIcon(alert.currentStateOfTrip.currentSpeed)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Divider */}
                                    {index !== alertsList.length - 1 && <hr className="my-1.5 border-[#aaaaaa3d]" />}
                                </div>
                            ))}
                        </CardContent>
                    </div>
                </Card>
            )}

            {/* Minimized View */}
            {isMinimized && (
                <div
                    className="fixed bottom-15 left-6 bg-white shadow-lg border rounded-lg px-4 py-2 flex items-center justify-between w-60 cursor-pointer"
                    onClick={() => setIsMinimized(false)}>
                    <div className="flex items-center gap-2">
                        <FcAdvertising className="size-5" />
                        <span className="font-medium">{t('reports.newAlerts')}</span>
                    </div>
                    <MdAdd size={30} className="border border-gray-300 rounded p-1 text-gray-500 hover:text-gray-700" />
                </div>
            )}
        </>
    );
}
