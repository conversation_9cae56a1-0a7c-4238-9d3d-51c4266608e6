'use client';

import { useTranslation } from 'react-i18next';
import { FilterIcon, ListChecks, Route } from 'lucide-react';

import { useTripFilterStore } from '@/stores/tripFilterStore';
import { Card, CardContent } from '@/components/common/ui/Card';
import { Radio } from '@/components/common/ui/Radio';
import { Checkbox } from '@/components/common/ui/Checkbox';

const TRIP_CATEGORY_OPTIONS = [
    { key: 'myRoutes', label: 'myRoutes' },
    { key: 'suspicious', label: 'suspiciousTrips' },
    { key: 'focused', label: 'focusedTrips' },
    { key: 'stopped', label: 'stoppedTrips' },
] as const;

const TRIP_STATUS_OPTIONS = [
    { label: 'insideEntryPort', value: 'entryPort' },
    { label: 'inExitPort', value: 'exitPort' },
    { label: 'onRoute', value: 'onRoute' },
] as const;

export default function RoutesTap() {
    const { t, i18n } = useTranslation();
    const { useFilters, tripCategory, tripStatus, setUseFilters, setTripCategory, toggleTripStatus } =
        useTripFilterStore();

    return (
        <Card className="_effect" dir={i18n.dir()}>
            <CardContent className="p-0">
                {/* Scope Selection */}
                <div className="border-b border-gray-100 last:border-b-0 space-y-2 px-4 py-3">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <FilterIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-lg font-medium">{t('filter.scope')}</span>
                    </div>
                    <div className="space-y-1">
                        <Checkbox
                            id="useFilters"
                            label={t('filter.useFilters')}
                            checked={useFilters}
                            onChange={(checked) => setUseFilters(checked)}
                        />
                        <Checkbox
                            id="allTrips"
                            label={t('filter.allTrips')}
                            checked={!useFilters}
                            disabled={useFilters}
                        />
                    </div>
                </div>

                {/* Trip Category */}
                <div className="border-b border-gray-100 last:border-b-0 space-y-2 px-4 py-3">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <Route className="w-4 h-4 text-gray-500" />
                        <span className="text-lg font-medium">{t('filter.tripCategory')}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                        {TRIP_CATEGORY_OPTIONS.map(({ key, label }) => (
                            <Radio
                                key={key}
                                name="tripCategory"
                                label={t(`filter.${label}`)}
                                checked={tripCategory === key}
                                onChange={() => setTripCategory(key)}
                                disabled={!useFilters}
                            />
                        ))}
                    </div>
                </div>

                {/* Trip Status — always enabled */}
                <div className="space-y-2 px-4 py-3">
                    <div className="flex items-center gap-2">
                        <ListChecks className="w-4 h-4 text-gray-500" />
                        <span className="text-lg font-medium">{t('filter.tripStatus')}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                        {TRIP_STATUS_OPTIONS.map(({ label, value }) => (
                            <Checkbox
                                key={value}
                                id={value}
                                label={t(`filter.${label}`)}
                                checked={tripStatus.includes(value)}
                                onChange={() => toggleTripStatus(value)}
                            />
                        ))}
                    </div>
                </div>

                {/* Footer */}
                <div className="px-2 py-2 bg-gray-50 border-t border-gray-100">
                    <p className="text-xs text-gray-500 text-center flex items-center justify-center gap-1">
                        <span className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></span>
                        {t('common.changesApplyRealTime')}
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}
