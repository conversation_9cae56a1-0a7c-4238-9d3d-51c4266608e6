import { useTranslation } from 'react-i18next';
import { FcSearch } from 'react-icons/fc';
import { PiMapPinSimpleAreaFill } from 'react-icons/pi';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/ui/Card';
import SearchField from '@/components/common/ui/SearchField';
import SwitchButton from '@/components/common/ui/SwitchButton';
import { type ChartConfig } from '@/components/common/ui/Chart';
import LineChart from '@/components/common/ui/LineChart';

export default function SearchTap() {
    const { t } = useTranslation();

    const chartData = [
        { month: 'January', active: 186, inActive: 80 },
        { month: 'February', active: 305, inActive: 200 },
        { month: 'March', active: 237, inActive: 120 },
        { month: 'April', active: 73, inActive: 190 },
        { month: 'May', active: 209, inActive: 130 },
        { month: 'June', active: 214, inActive: 140 },
    ];

    const chartConfig = {
        active: {
            label: 'Active',
            color: '#2563eb',
        },
        inActive: {
            label: 'inActive',
            color: '#60a5fa',
        },
    } satisfies ChartConfig;

    return (
        <Card>
            <CardContent className="grid gap-4">
                <CardHeader className="px-0">
                    <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <FcSearch className="size-5 text-[#aaa]" />
                            {t('common.search')}
                        </div>
                        <button className="flex items-center gap-2 text-red-500">
                            <PiMapPinSimpleAreaFill />
                            <span className="text-[14px]"> {t('common.measureDistance')} </span>
                        </button>
                    </CardTitle>
                </CardHeader>

                <div>
                    <SearchField />

                    <div className="flex items-center justify-between px-2 mt-4.5">
                        <SwitchButton id="ShowPort" label="common.showPorts" />
                        <SwitchButton id="ShowCheckPoint" label="common.showCheckPoint" />
                    </div>
                </div>

                <div className="w-[320px] mt-4">
                    <LineChart chartConfig={chartConfig} chartData={chartData} dataKey="month" />
                </div>
            </CardContent>
        </Card>
    );
}
