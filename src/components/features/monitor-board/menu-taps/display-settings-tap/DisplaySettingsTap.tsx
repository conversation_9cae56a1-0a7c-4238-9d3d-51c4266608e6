/**
 * DisplaySettingsTab Component
 * --------------------------------------
 * This component renders a settings panel for controlling the display of trips, checkpoints, and ports
 * on a live monitoring map. It provides:
 *  - Trip display mode toggle (cluster vs. individual trips)
 *  - Checkpoint visibility toggles (customs, police, suspicious geofences)
 *  - Port visibility toggles (land, sea, air)
 *
 * Features:
 *  - Fully type-safe integration with Zustand store
 *  - Instant real-time updates on the map view
 *  - Internationalization (i18n) support for RTL and LTR languages
 */

import React from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox } from '@/components/common/ui/Checkbox';
import { Radio } from '@/components/common/ui/Radio';
import { Card, CardContent } from '@/components/common/ui/Card';

import { useDisplaySettingsStore } from './displaySettingsStore';
import { type Option, type TripDisplayMode, type CheckpointType, type PortType } from './types';

const DisplaySettingsTab: React.FC = () => {
    const { t, i18n } = useTranslation();
    const dir = i18n.language === 'ar' ? 'rtl' : 'ltr';

    // Zustand store subscription
    const { settings, setTripDisplayMode, toggleCheckpoint, togglePort } = useDisplaySettingsStore();

    // Configurable options
    const tripModeOptions: Option<TripDisplayMode>[] = [
        { key: 'cluster', label: t('displaySettingsTab.clusterTrips'), default: true },
        { key: 'individual', label: t('displaySettingsTab.individualTrips') },
    ];

    const checkpointOptions: Option<CheckpointType>[] = [
        { key: 'customs', label: t('displaySettingsTab.customsCheckpoints') },
        { key: 'police', label: t('displaySettingsTab.policeCheckpoints') },
        { key: 'suspiciousGeofences', label: t('displaySettingsTab.suspiciousGeofences') },
    ];

    const portOptions: Option<PortType>[] = [
        { key: 'land', label: t('displaySettingsTab.landPorts') },
        { key: 'sea', label: t('displaySettingsTab.seaports') },
        { key: 'air', label: t('displaySettingsTab.airports') },
    ];

    /**
     * Renders a reusable settings section with a title, icon, and a list of radio buttons or checkboxes.
     *
     * @template K - The key type representing the store field (e.g., 'cluster', 'land', 'police')
     * @param title - The translated section title displayed in the UI
     * @param icon - An emoji or icon representing the section
     * @param options - List of selectable options with labels
     * @param checkedMap - Object mapping each option key to its boolean checked state
     * @param toggleFn - Callback to toggle a specific option in the store
     * @param type - The input type ('radio' for exclusive selection, 'checkbox' for multi-select)
     */
    const renderSection = <K extends string>(
        title: string,
        icon: string,
        options: Option<K>[],
        checkedMap: Record<K, boolean>,
        toggleFn: (key: K) => void,
        type: 'radio' | 'checkbox',
    ) => (
        <div className="border-b border-gray-100 last:border-b-0">
            <div className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700">
                <span>{icon}</span>
                {title}
            </div>
            <div className="px-4 pb-4 space-y-1">
                {options.map(({ key, label }) =>
                    type === 'radio' ? (
                        <Radio
                            key={key}
                            label={label}
                            name="trip-display-mode"
                            checked={(settings.tripDisplayMode as K) === key}
                            onChange={() => setTripDisplayMode(key as TripDisplayMode)}
                        />
                    ) : (
                        <Checkbox key={key} label={label} checked={checkedMap[key]} onChange={() => toggleFn(key)} />
                    ),
                )}
            </div>
        </div>
    );

    return (
        <Card className="_effect" dir={dir}>
            <CardContent className="p-0">
                {renderSection(
                    t('displaySettingsTab.tripDisplayMode'),
                    '🗺️',
                    tripModeOptions,
                    {} as Record<TripDisplayMode, boolean>,
                    () => {},
                    'radio',
                )}
                {renderSection(
                    t('displaySettingsTab.checkpoints'),
                    '🛡️',
                    checkpointOptions,
                    settings.checkpoints,
                    toggleCheckpoint,
                    'checkbox',
                )}
                {renderSection(
                    t('displaySettingsTab.ports'),
                    '🏭',
                    portOptions,
                    settings.ports,
                    togglePort,
                    'checkbox',
                )}

                {/* Footer */}
                <div className="px-2 py-2 bg-gray-50 border-t border-gray-100">
                    <p className="text-xs text-gray-500 text-center flex items-center justify-center gap-1">
                        <span className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></span>
                        {t('displaySettingsTab.changesApplyRealTime')}
                    </p>
                </div>
            </CardContent>
        </Card>
    );
};

export default DisplaySettingsTab;
