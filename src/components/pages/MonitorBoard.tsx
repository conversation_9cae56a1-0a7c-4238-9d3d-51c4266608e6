import { GoogleMap, type MapPoint, type TripLocation } from '@/components/common/google-map/GoogleMap';

import MonitorMenu from '../features/monitor-board/MonitorMenu';

const dummyPoints: MapPoint[] = [
    {
        Id: 1,
        Name: { Arabic: 'الرياض', English: 'Riyadh' },
        Type: 0,
        EntryType: 0,
        Lat: 24.7136,
        Long: 46.6753,
        ContactNumber: '123',
    },
    {
        Id: 2,
        Name: { Arabic: 'جدة', English: 'Jeddah' },
        Type: 0,
        EntryType: 0,
        Lat: 21.3891,
        Long: 39.8579,
        ContactNumber: '456',
    },
    {
        Id: 3,
        Name: { Arabic: 'الدمام', English: 'Dammam' },
        Type: 0,
        EntryType: 0,
        Lat: 26.4207,
        Long: 50.0888,
        ContactNumber: '789',
    },
];

const dummyTrips: TripLocation[] = [
    { id: 101, current_location: { lat: 25.75, long: 46.65 }, angle: 45 },
    { id: 102, current_location: { lat: 22.4, long: 39.85 }, angle: 90 },
    { id: 103, current_location: { lat: 27.42, long: 50.1 }, angle: 270 },
];

export default function MonitorBoard() {
    return (
        <section className="relative h-[100%]">
            <div className="absolute top-[1rem] right-10 z-10 w-full max-w-sm">
                <MonitorMenu />
            </div>

            <GoogleMap points={dummyPoints} tripLocations={dummyTrips} />
        </section>
    );
}
