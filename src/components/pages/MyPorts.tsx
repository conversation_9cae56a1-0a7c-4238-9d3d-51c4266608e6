import { useTranslation } from 'react-i18next';
import { HiLockClosed } from 'react-icons/hi2';
import { IoMdCheckmarkCircleOutline } from 'react-icons/io';
import { IoArrowDownOutline } from 'react-icons/io5';
import { MdCheck, MdNotificationsActive, MdOutlineNotificationsOff } from 'react-icons/md';
import { RiArrowLeftUpLine } from 'react-icons/ri';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { TbBatteryVertical } from 'react-icons/tb';

import Charging from '@imgs/charging.svg';
import Tooltip from '@/components/common/ui/Tooltip';
import FilterButton from '@/components/common/filter-button/FilterButton';
import SummaryCard from '@/components/common/ui/SummaryCard';
import type { SummaryCardDetails } from '@/components/common/ui/SummaryCard';
import { useMyPortStore } from '@/stores/myPortStore';

import MyPortsDetails from '../features/my-ports/MyPortsDetails';

export default function MyPorts() {
    const { t } = useTranslation();

    const myPorts = useMyPortStore((state) => state.myPorts);

    const summaryCardsDetails: SummaryCardDetails[] = [
        {
            title: t('common.activeTripsWithAlerts'),
            icon: <MdNotificationsActive />,
            value: 1500,
        },
        {
            title: t('common.activeTripsWithoutAlerts'),
            icon: <MdOutlineNotificationsOff />,
            value: 12,
        },
        {
            title: t('common.totalActiveTrips'),
            icon: <IoMdCheckmarkCircleOutline />,
            value: 1350,
        },
        { title: t('common.totalClosedTrips'), icon: <HiLockClosed />, value: 18 },
    ];

    return (
        <>
            <div className="flex flex-col pt-4">
                <div className="flex items-center justify-between flex-wrap px-4 gap-3">
                    <div className="flex gap-3 mb-4 flex-grow-1">
                        {summaryCardsDetails.map((item, index) => (
                            <SummaryCard details={item} key={index} />
                        ))}
                    </div>

                    <div className="flex flex-wrap justify-end gap-3 pe-3 flex-grow-1 mb-4">
                        <FilterButton />
                    </div>
                </div>

                <div className="_table_container flex-grow-1">
                    <DataTable
                        value={myPorts}
                        tableStyle={{ minWidth: '50rem' }}
                        scrollable
                        scrollHeight="62vh"
                        paginator
                        rows={50}>
                        <Column header={t('common.transitNumber')} body={() => <MyPortsDetails />}></Column>
                        <Column body={(rowData) => rowData.transitNumber} header={t('common.transitNumber')}></Column>
                        <Column
                            body={(rowData) => rowData.shipmentDescription}
                            header={t('common.shipmentDescription')}></Column>
                        <Column
                            body={(rowData) => (
                                <>
                                    <p className="flex items-center min-w-[200px] gap-2 mb-1">
                                        <IoArrowDownOutline className="mt-1 size-4 text-blue-500" />
                                        {rowData.routeName}
                                    </p>
                                    <p className="flex items-center min-w-[200px] gap-2">
                                        <RiArrowLeftUpLine className="mt-1 size-4 text-red-500" />
                                        {rowData.routeName}
                                    </p>
                                </>
                            )}
                            header={` ${t('common.portIn')} - ${t('common.portOut')} `}></Column>

                        <Column body="" header={t('common.latestInformationAboutTheTarget')}></Column>
                        <Column body={(rowData) => rowData.trackerSerialNumber} header={t('common.tracker')}></Column>
                        <Column body={(rowData) => rowData.driver.name} header={t('common.driver')}></Column>
                        <Column body={(rowData) => rowData.truck.plateNumber} header={t('common.truck')}></Column>
                        <Column body="-" header={t('common.warnings')}></Column>
                        <Column
                            body={() => (
                                <div className="flex gap-1 items-center justify-center">
                                    <Tooltip tooltipMessage="filter.active">
                                        <MdCheck className="m-auto text-[green] size-5" />
                                    </Tooltip>

                                    <Tooltip tooltipMessage="common.batteryLife" translationParams={{ value: '0%' }}>
                                        <TbBatteryVertical className="m-auto size-5" />
                                    </Tooltip>

                                    <Tooltip tooltipMessage="common.noCharging">
                                        <img
                                            src={Charging}
                                            alt="Charging"
                                            width="20px"
                                            height="20px"
                                            style={{ maxWidth: 'fit-content' }}
                                        />
                                    </Tooltip>
                                </div>
                            )}
                            header={t('common.status')}></Column>
                    </DataTable>
                </div>
            </div>
        </>
    );
}
