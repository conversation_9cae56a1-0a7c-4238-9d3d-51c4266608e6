// import { logger } from '../logging/logger';
// import { validator } from '../validation';
// import { User, LoginCredentials, AuthResponse, ApiError } from '../../../../../../Advanced Photonics/Code Samples/dashboard samples 29-7/src/shared/types';

// // Demo static credentials for testing
// const DEMO_CREDENTIALS = {
//   username: 'admin',
//   password: 'demo123'
// };

// // Demo user data
// const DEMO_USER: User = {
//   id: 'demo-user-123',
//   username: 'admin',
//   email: '<EMAIL>',
//   token: 'demo-jwt-token-12345'
// };

// export class AuthService {
//   private static instance: AuthService;
//   private user: User | null = null;
//   private refreshTimeout: any = null;

//   private constructor() {
//     logger.debug('AuthService: Initializing singleton instance');
//     this.loadUserFromStorage();
//   }

//   public static getInstance(): AuthService {
//     if (!AuthService.instance) {
//       AuthService.instance = new AuthService();
//     }
//     return AuthService.instance;
//   }

//   // Check if user is authenticated
//     public isAuthenticated(): boolean {
//     const hasUser = this.user !== null;
//     const isValid = this.isTokenValid();
//     logger.debug('AuthService.isAuthenticated check', {
//       hasUser,
//       isValid,
//       token: this.user?.token ? 'present' : 'missing',
//       user: this.user ? { id: this.user.id, username: this.user.username } : null
//     });
//     return hasUser && isValid;
//   }

//   // Get current user
//   public getCurrentUser(): User | null {
//     return this.user;
//   }

//   // Get auth token
//   public getToken(): string | null {
//     return this.user?.token || null;
//   }

//   // Initialize auth state from storage
//   public initializeFromStorage(): void {
//     this.loadUserFromStorage();
//   }

//   // Login with credentials
//   public async login(credentials: LoginCredentials): Promise<AuthResponse> {
//     const timer = logger.startTimer('Authentication');

//     try {
//       // Validate credentials
//       const validationResult = validator.validateLoginCredentials(credentials);
//       if (!validationResult.success) {
//         const errorMsg = validator.formatValidationErrors(validationResult.errors || []).join(', ');
//         logger.warn('Login validation failed', { errors: validationResult.errors });
//         throw new Error(`Invalid credentials: ${errorMsg}`);
//       }

//       logger.info('Attempting login', { username: credentials.username });

//       // For demo: Check against static credentials
//       if (
//         credentials.username === DEMO_CREDENTIALS.username &&
//         credentials.password === DEMO_CREDENTIALS.password
//       ) {
//         // Simulate API response delay
//         await new Promise(resolve => setTimeout(resolve, 500));

//         // Generate demo JWT token with expiration
//         const demoToken = this.generateDemoToken();
//         const authUser: User = {
//           ...DEMO_USER,
//           token: demoToken
//         };

//         const authResponse: AuthResponse = {
//           user: authUser,
//           token: demoToken,
//           refreshToken: 'demo-refresh-token-67890'
//         };

//         // Store user data
//         this.user = authUser;
//         this.saveUserToStorage();
//         this.scheduleTokenRefresh();

//         // Verify storage was successful
//         const storedUser = localStorage.getItem('auth_user');
//         const storedToken = localStorage.getItem('auth_token');
//         logger.debug('AuthService: Verifying login storage', {
//           hasStoredUser: !!storedUser,
//           hasStoredToken: !!storedToken,
//           userId: authUser.id
//         });

//         timer();
//         logger.info('Login successful', { userId: authUser.id, username: authUser.username });
//         logger.logUserAction('LOGIN', { username: credentials.username });

//         return authResponse;
//       } else {
//         timer();
//         logger.warn('Login failed - invalid credentials', { username: credentials.username });

//         const error: ApiError = {
//           message: 'Invalid username or password',
//           code: 'INVALID_CREDENTIALS'
//         };
//         throw error;
//       }
//     } catch (error) {
//       timer();
//       logger.error('Login error', error as Error, { username: credentials.username });

//       if (error instanceof Error || (error && typeof error === 'object' && 'code' in error)) {
//         throw error;
//       }

//       const authError: ApiError = {
//         message: 'Authentication failed',
//         code: 'AUTH_ERROR',
//         details: error
//       };
//       throw authError;
//     }
//   }

//   // Logout
//   public logout(): void {
//     try {
//       const username = this.user?.username;

//       // Clear user data
//       this.user = null;
//       this.clearUserFromStorage();
//       this.clearTokenRefresh();

//       logger.info('Logout successful', { username });
//       logger.logUserAction('LOGOUT', { username });
//     } catch (error) {
//       logger.error('Logout error', error as Error);
//     }
//   }

//   // Refresh token
//   public async refreshToken(): Promise<string> {
//     try {
//       if (!this.user) {
//         throw new Error('No user to refresh token for');
//       }

//       logger.debug('Refreshing token', { userId: this.user.id });

//       // For demo: Generate new token
//       const newToken = this.generateDemoToken();
//       this.user.token = newToken;
//       this.saveUserToStorage();
//       this.scheduleTokenRefresh();

//       logger.debug('Token refreshed successfully');
//       return newToken;
//     } catch (error) {
//       logger.error('Token refresh failed', error as Error);

//       // Force logout on refresh failure
//       this.logout();

//       const refreshError: ApiError = {
//         message: 'Token refresh failed',
//         code: 'REFRESH_FAILED',
//         details: error
//       };
//       throw refreshError;
//     }
//   }

//   // Check if token is valid (not expired)
//   private isTokenValid(): boolean {
//     if (!this.user?.token) {
//       logger.debug('AuthService.isTokenValid: No token present');
//       return false;
//     }

//     try {
//       // For demo: Parse the demo token to check expiration
//       const tokenData = this.parseDemoToken(this.user.token);
//       const currentTime = Math.floor(Date.now() / 1000); // Convert to seconds
//       const isValid = tokenData.exp > currentTime;

//       logger.debug('AuthService.isTokenValid: Token validation', {
//         currentTime,
//         expirationTime: tokenData.exp,
//         timeUntilExpiry: tokenData.exp - currentTime,
//         isValid
//       });

//       return isValid;
//     } catch (error) {
//       logger.debug('AuthService.isTokenValid: Token validation failed', { error });
//       return false;
//     }
//   }

//   // Generate demo JWT token
//   private generateDemoToken(): string {
//     const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
//     const now = Math.floor(Date.now() / 1000); // Current time in seconds
//     const payload = btoa(JSON.stringify({
//       sub: DEMO_USER.id,
//       username: DEMO_USER.username,
//       email: DEMO_USER.email,
//       iat: now,
//       exp: now + (24 * 60 * 60), // 24 hours expiration for development
//       iss: 'tracker-demo'
//     }));
//     const signature = btoa('demo-signature');

//     return `${header}.${payload}.${signature}`;
//   }

//   // Parse demo JWT token
//   private parseDemoToken(token: string): any {
//     try {
//       const parts = token.split('.');
//       if (parts.length !== 3) {
//         throw new Error('Invalid token format');
//       }

//       const payload = JSON.parse(atob(parts[1]));
//       logger.debug('AuthService.parseDemoToken: Token parsed', {
//         sub: payload.sub,
//         username: payload.username,
//         iat: new Date(payload.iat * 1000).toISOString(),
//         exp: new Date(payload.exp * 1000).toISOString()
//       });
//       return payload;
//     } catch (error) {
//       logger.error('AuthService.parseDemoToken: Token parsing failed', error as Error);
//       throw new Error('Invalid token');
//     }
//   }

//   // Save user to localStorage
//   private saveUserToStorage(): void {
//     try {
//       if (!this.user) {
//         logger.debug('AuthService.saveUserToStorage: No user to save');
//         return;
//       }

//       // First clear any existing data
//       this.clearUserFromStorage();

//       // Save new user data
//       const userData = JSON.stringify(this.user);
//       localStorage.setItem('auth_user', userData);
//       localStorage.setItem('auth_token', this.user.token);

//       // Verify the save was successful
//       const savedUser = localStorage.getItem('auth_user');
//       const savedToken = localStorage.getItem('auth_token');

//       if (!savedUser || !savedToken) {
//         throw new Error('Failed to verify saved auth data');
//       }

//       logger.debug('AuthService.saveUserToStorage: User data saved successfully', {
//         userId: this.user.id,
//         username: this.user.username,
//         tokenLength: this.user.token.length
//       });
//     } catch (error) {
//       logger.error('AuthService.saveUserToStorage: Failed to save user data', error as Error);
//       // Clear any partial data
//       this.clearUserFromStorage();
//       throw error;
//     }
//   }

//   // Load user from localStorage
//   private loadUserFromStorage(): void {
//     try {
//       logger.debug('AuthService.loadUserFromStorage: Attempting to load from storage');

//       // Clear any existing user data first
//       this.user = null;

//       const storedUser = localStorage.getItem('auth_user');
//       const storedToken = localStorage.getItem('auth_token');

//       logger.debug('AuthService.loadUserFromStorage: Storage check', {
//         hasStoredUser: !!storedUser,
//         hasStoredToken: !!storedToken
//       });

//       if (!storedUser || !storedToken) {
//         logger.debug('AuthService.loadUserFromStorage: No stored auth data found');
//         return;
//       }

//       let user: User;
//       try {
//         user = JSON.parse(storedUser);
//         logger.debug('AuthService.loadUserFromStorage: Parsed stored user', {
//           userId: user.id,
//           username: user.username,
//           tokenLength: user.token?.length || 0
//         });
//       } catch (parseError) {
//         logger.error('AuthService.loadUserFromStorage: Failed to parse stored user', parseError as Error);
//         this.clearUserFromStorage();
//         return;
//       }

//       // First check if tokens match
//       if (user.token !== storedToken) {
//         logger.debug('AuthService.loadUserFromStorage: Token mismatch');
//         this.clearUserFromStorage();
//         return;
//       }

//       // Set user before validating token since isTokenValid needs it
//       this.user = user;

//       // Then check if token is valid
//       const isValid = this.isTokenValid();
//       logger.debug('AuthService.loadUserFromStorage: Token validation', { isValid });

//       if (isValid) {
//         this.scheduleTokenRefresh();
//         logger.info('AuthService.loadUserFromStorage: Successfully restored user session', {
//           username: user.username,
//           userId: user.id
//         });
//       } else {
//         logger.debug('AuthService.loadUserFromStorage: Stored token is invalid or expired');
//         this.user = null;
//         this.clearUserFromStorage();
//       }
//     } catch (error) {
//       logger.error('AuthService.loadUserFromStorage: Failed to load from storage', error as Error);
//       this.user = null;
//       this.clearUserFromStorage();
//     }
//   }

//   // Clear user from localStorage
//   private clearUserFromStorage(): void {
//     try {
//       localStorage.removeItem('auth_user');
//       localStorage.removeItem('auth_token');
//       logger.debug('User data cleared from storage');
//     } catch (error) {
//       logger.warn('Failed to clear user from storage', { error });
//     }
//   }

//   // Schedule automatic token refresh
//   private scheduleTokenRefresh(): void {
//     this.clearTokenRefresh();

//     if (!this.user?.token) {
//       return;
//     }

//     try {
//       const tokenData = this.parseDemoToken(this.user.token);
//       const expirationTime = tokenData.exp * 1000; // Convert seconds to milliseconds
//       const refreshTime = expirationTime - (5 * 60 * 1000); // Refresh 5 minutes before expiration
//       const timeUntilRefresh = refreshTime - Date.now();

//       if (timeUntilRefresh > 0) {
//         this.refreshTimeout = setTimeout(() => {
//           this.refreshToken().catch(error => {
//             logger.error('Automatic token refresh failed', error);
//           });
//         }, timeUntilRefresh);

//         logger.debug('Token refresh scheduled', { timeUntilRefresh });
//       }
//     } catch (error) {
//       logger.warn('Failed to schedule token refresh', { error });
//     }
//   }

//   // Clear token refresh timeout
//   private clearTokenRefresh(): void {
//     if (this.refreshTimeout) {
//       clearTimeout(this.refreshTimeout);
//       this.refreshTimeout = null;
//     }
//   }

//   // Get user permissions (for demo)
//   public getUserPermissions(): string[] {
//     if (!this.isAuthenticated()) {
//       return [];
//     }

//     // Demo permissions
//     return ['read:trackers', 'write:trackers', 'admin:dashboard'];
//   }

//   // Check if user has specific permission
//   public hasPermission(permission: string): boolean {
//     const permissions = this.getUserPermissions();
//     for (let i = 0; i < permissions.length; i++) {
//       if (permissions[i] === permission) {
//         return true;
//       }
//     }
//     return false;
//   }

//   // Get authentication header for API requests
//   public getAuthHeaders(): Record<string, string> {
//     const token = this.getToken();
//     if (!token) {
//       return {};
//     }

//     return {
//       'Authorization': `Bearer ${token}`
//     };
//   }

//   // Handle authentication errors (e.g., from API responses)
//   public handleAuthError(error: ApiError): void {
//     logger.warn('Authentication error received', { error });

//     // Force logout on authentication errors
//     if (error.code === 'UNAUTHORIZED' || error.code === 'TOKEN_EXPIRED') {
//       logger.info('Forcing logout due to auth error');
//       this.logout();
//     }
//   }
// }

// // Export singleton instance
// export const authService = AuthService.getInstance();
