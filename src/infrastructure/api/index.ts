// import { QueryClient, useQuery, useMutation, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
// import { logger } from '../logging/logger';
// import { validator } from '../validation';
// import { FetchyConfig, FetchyOptions, FetchyResponse, ApiResponse, ApiError } from '../../../../../../Advanced Photonics/Code Samples/dashboard samples 29-7/src/shared/types';

// // Default configuration
// const DEFAULT_CONFIG: FetchyConfig = {
//   baseURL: 'http://localhost:3001/api',
//   timeout: 30000,
//   retries: 3,
//   retryDelay: 1000
// };

// export class Fetchy {
//   private static instance: Fetchy;
//   private config: FetchyConfig;
//   private queryClient: QueryClient;

//   private constructor(config: Partial<FetchyConfig> = {}) {
//     this.config = { ...DEFAULT_CONFIG, ...config };
//     this.queryClient = new QueryClient({
//       defaultOptions: {
//         queries: {
//           retry: this.config.retries,
//           retryDelay: this.config.retryDelay,
//           staleTime: 5 * 60 * 1000, // 5 minutes
//           gcTime: 10 * 60 * 1000, // 10 minutes
//         },
//         mutations: {
//           retry: this.config.retries,
//           retryDelay: this.config.retryDelay,
//         },
//       },
//     });

//     logger.info('Fetchy initialized', { config: this.config });
//   }

//   public static getInstance(config?: Partial<FetchyConfig>): Fetchy {
//     if (!Fetchy.instance) {
//       Fetchy.instance = new Fetchy(config);
//     }
//     return Fetchy.instance;
//   }

//   public getQueryClient(): QueryClient {
//     return this.queryClient;
//   }

//   // Private method to build complete URL
//   private buildUrl(endpoint: string): string {
//     if (endpoint.startsWith('http')) {
//       return endpoint;
//     }
//     const baseUrl = this.config.baseURL.endsWith('/') ? this.config.baseURL : `${this.config.baseURL}/`;
//     const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
//     return `${baseUrl}${cleanEndpoint}`;
//   }

//   // Private method to build query string
//   private buildQueryString(params: Record<string, any>): string {
//     if (!params || typeof params !== 'object') return '';

//     const searchParams = new URLSearchParams();
//     for (const key in params) {
//       if (params[key] !== undefined && params[key] !== null) {
//         searchParams.append(key, String(params[key]));
//       }
//     }
//     return searchParams.toString();
//   }

//   // Core HTTP request method
//   private async makeRequest<T>(
//     endpoint: string,
//     options: FetchyOptions = {}
//   ): Promise<FetchyResponse<T>> {
//     const startTime = performance.now();
//     const url = this.buildUrl(endpoint);
//     const method = options.method || 'GET';

//     // Add query parameters for GET requests
//     let finalUrl = url;
//     if (method === 'GET' && options.params) {
//       const queryString = this.buildQueryString(options.params);
//       finalUrl = queryString ? `${url}?${queryString}` : url;
//     }

//     // Log the request
//     logger.logApiRequest(method, finalUrl, options.body);

//     try {
//       // Prepare headers
//       const headers: Record<string, string> = {
//         'Content-Type': 'application/json',
//         ...options.headers,
//       };

//       // Get auth token if available
//       const token = this.getAuthToken();
//       if (token) {
//         headers.Authorization = `Bearer ${token}`;
//       }

//       // Prepare fetch options
//       const fetchOptions: RequestInit = {
//         method,
//         headers,
//         signal: AbortSignal.timeout(options.timeout || this.config.timeout),
//       };

//       // Add body for non-GET requests
//       if (method !== 'GET' && options.body) {
//         fetchOptions.body = JSON.stringify(options.body);
//       }

//       // Make the request
//       const response = await fetch(finalUrl, fetchOptions);
//       const duration = performance.now() - startTime;

//       // Log the response
//       logger.logApiResponse(method, finalUrl, response.status, duration);

//       // Parse response
//       let data: T;
//       const contentType = response.headers.get('content-type');

//       if (contentType && contentType.includes('application/json')) {
//         data = await response.json();
//       } else {
//         data = (await response.text()) as unknown as T;
//       }

//       // Build response headers object
//       const responseHeaders: Record<string, string> = {};
//       response.headers.forEach((value, key) => {
//         responseHeaders[key] = value;
//       });

//       const fetchyResponse: FetchyResponse<T> = {
//         data,
//         status: response.status,
//         statusText: response.statusText,
//         headers: responseHeaders,
//       };

//       // Handle non-2xx responses
//       if (!response.ok) {
//         const error: ApiError = {
//           message: `HTTP ${response.status}: ${response.statusText}`,
//           code: `HTTP_${response.status}`,
//           details: data,
//         };

//         logger.error(`API request failed: ${method} ${finalUrl}`, undefined, {
//           status: response.status,
//           error: data
//         });

//         throw error;
//       }

//       return fetchyResponse;

//     } catch (error) {
//       const duration = performance.now() - startTime;

//       if (error instanceof Error) {
//         logger.error(`API request error: ${method} ${finalUrl}`, error, { duration });

//         // Handle specific error types
//         if (error.name === 'AbortError') {
//           const timeoutError: ApiError = {
//             message: 'Request timeout',
//             code: 'TIMEOUT',
//             details: { timeout: options.timeout || this.config.timeout }
//           };
//           throw timeoutError;
//         }

//         if (error.name === 'TypeError' && error.message.includes('fetch')) {
//           const networkError: ApiError = {
//             message: 'Network error - please check your connection',
//             code: 'NETWORK_ERROR',
//             details: error.message
//           };
//           throw networkError;
//         }
//       }

//       // Re-throw API errors
//       if (error && typeof error === 'object' && 'code' in error) {
//         throw error;
//       }

//       // Wrap unknown errors
//       const unknownError: ApiError = {
//         message: 'An unexpected error occurred',
//         code: 'UNKNOWN_ERROR',
//         details: error
//       };
//       throw unknownError;
//     }
//   }

//   // Get authentication token from storage
//   private getAuthToken(): string | null {
//     try {
//       return localStorage.getItem('auth_token');
//     } catch {
//       return null;
//     }
//   }

//   // Public HTTP methods
//   public async get<T>(endpoint: string, params?: Record<string, any>, options?: Omit<FetchyOptions, 'method' | 'body'>): Promise<FetchyResponse<T>> {
//     return this.makeRequest<T>(endpoint, { ...options, method: 'GET', params });
//   }

//   public async post<T>(endpoint: string, body?: any, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
//     return this.makeRequest<T>(endpoint, { ...options, method: 'POST', body });
//   }

//   public async put<T>(endpoint: string, body?: any, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
//     return this.makeRequest<T>(endpoint, { ...options, method: 'PUT', body });
//   }

//   public async patch<T>(endpoint: string, body?: any, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
//     return this.makeRequest<T>(endpoint, { ...options, method: 'PATCH', body });
//   }

//   public async delete<T>(endpoint: string, options?: Omit<FetchyOptions, 'method' | 'body'>): Promise<FetchyResponse<T>> {
//     return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' });
//   }

//   // Configuration methods
//   public updateConfig(newConfig: Partial<FetchyConfig>): void {
//     this.config = { ...this.config, ...newConfig };
//     logger.info('Fetchy configuration updated', { config: this.config });
//   }

//   public getConfig(): FetchyConfig {
//     return { ...this.config };
//   }

//   // Query Client methods for advanced usage
//   public invalidateQueries(queryKey: string[]): Promise<void> {
//     logger.debug('Invalidating queries', { queryKey });
//     return this.queryClient.invalidateQueries({ queryKey });
//   }

//   public removeQueries(queryKey: string[]): void {
//     logger.debug('Removing queries', { queryKey });
//     this.queryClient.removeQueries({ queryKey });
//   }

//   public prefetchQuery<T>(
//     queryKey: string[],
//     queryFn: () => Promise<T>,
//     options?: { staleTime?: number }
//   ): Promise<void> {
//     logger.debug('Prefetching query', { queryKey });
//     return this.queryClient.prefetchQuery({
//       queryKey,
//       queryFn,
//       staleTime: options?.staleTime,
//     });
//   }
// }

// Export singleton instance

// export const fetchy = Fetchy.getInstance();

// // Export hooks for React components
// export const useFetchyQuery = <T>(
//   queryKey: string[],
//   queryFn: () => Promise<FetchyResponse<T>>,
//   options?: Omit<UseQueryOptions<FetchyResponse<T>>, 'queryKey' | 'queryFn'>
// ) => {
//   return useQuery({
//     queryKey,
//     queryFn,
//     ...options,
//   });
// };

// export const useFetchyMutation = <TData, TVariables>(
//   mutationFn: (variables: TVariables) => Promise<FetchyResponse<TData>>,
//   options?: UseMutationOptions<FetchyResponse<TData>, ApiError, TVariables>
// ) => {
//   return useMutation({
//     mutationFn,
//     ...options,
//   });
// };
