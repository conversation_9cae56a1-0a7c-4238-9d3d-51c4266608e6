// import { fetchy } from './index';
// import { Tracker } from '../validation/schemas';
// import { logger } from '../logging/logger';
// import { FilterCriteria } from '../../../../../../Advanced Photonics/Code Samples/dashboard samples 29-7/src/stores/filterStore';

// export class TrackerService {
//   private static instance: TrackerService;

//   private constructor() {}

//   public static getInstance(): TrackerService {
//     if (!TrackerService.instance) {
//       TrackerService.instance = new TrackerService();
//     }
//     return TrackerService.instance;
//   }

//   /**
//    * Get all active trackers from the API with optional filtering
//    * Example: Uses inline mapping for list transformation
//    */
//   public async getActiveTrackers(filterCriteria?: FilterCriteria): Promise<Tracker[]> {
//     try {
//       logger.info('TrackerService: Fetching active trackers', {
//         hasFilters: !!filterCriteria
//       });

//       // Inline mapping example - when API response format differs from app model
//       const response = await fetchy.get<any[]>('/trackers/active');

//       // List mapping with .map() - high performance for bulk transformation
//       const trackers = response.data.map(dto => ({
//         id: dto.id,
//         name: dto.vehicle_name,                    // API: vehicle_name → App: name
//         type: dto.vehicle_type,                    // API: vehicle_type → App: type
//         location: {
//           lat: dto.coordinates.latitude,           // API: coordinates.latitude → App: location.lat
//           lng: dto.coordinates.longitude,          // API: coordinates.longitude → App: location.lng
//           timestamp: new Date(dto.coordinates.recorded_at) // String → Date transformation
//         },
//         speed: dto.speed_kmh,                      // API: speed_kmh → App: speed
//         battery: dto.battery_percentage,           // API: battery_percentage → App: battery
//         status: dto.device_status === 'online' ? 'active' as const :     // Value transformation
//                 dto.device_status === 'offline' ? 'inactive' as const : 'maintenance' as const,
//         lastUpdate: new Date(dto.last_update_iso),        // String → Date
//         lastMaintenance: new Date(dto.last_maintenance_iso), // String → Date
//         isActive: dto.is_active,
//         color: dto.color_hex                       // API: color_hex → App: color
//       }));

//       // Apply filtering if criteria provided
//       const filteredData = filterCriteria
//         ? this.applyFilters(trackers, filterCriteria)
//         : trackers;

//       logger.info('TrackerService: Active trackers fetched and filtered', {
//         total: trackers.length,
//         filtered: filteredData.length,
//         wasFiltered: trackers.length !== filteredData.length
//       });

//       return filteredData;
//     } catch (error) {
//       logger.error('TrackerService: Failed to fetch active trackers', error as Error);
//       throw error;
//     }
//   }

//   /**
//    * Get trackers with optional filters
//    * Uses direct casting for performance
//    */
//   public async getTrackers(filters?: {
//     status?: 'active' | 'inactive' | 'maintenance';
//     vehicleType?: string;
//     limit?: number;
//   }): Promise<Tracker[]> {
//     try {
//       logger.info('TrackerService: Fetching trackers with filters', { filters });

//       // Direct casting - API response matches app model
//       const response = await fetchy.get<Tracker[]>('/trackers', filters);

//       return response.data;
//     } catch (error) {
//       logger.error('TrackerService: Failed to fetch trackers', error as Error);
//       throw error;
//     }
//   }

//   /**
//    * Get a specific tracker by ID
//    * Example: Uses inline mapping when API response differs from app model
//    */
//   public async getTrackerById(trackerId: string): Promise<Tracker> {
//     try {
//       logger.info('TrackerService: Fetching tracker by ID', { trackerId });

//       const response = await fetchy.get<any>(`/trackers/${trackerId}`);

//       // Inline mapping example - when API response format differs from app model
//       const tracker: Tracker = {
//         id: response.data.id,
//         name: response.data.vehicle_name,           // API: vehicle_name → App: name
//         type: response.data.vehicle_type,           // API: vehicle_type → App: type
//         location: {
//           lat: response.data.coordinates.latitude,   // API: coordinates.latitude → App: location.lat
//           lng: response.data.coordinates.longitude,  // API: coordinates.longitude → App: location.lng
//           timestamp: new Date(response.data.coordinates.recorded_at) // String → Date transformation
//         },
//         speed: response.data.speed_kmh,             // API: speed_kmh → App: speed
//         battery: response.data.battery_percentage,   // API: battery_percentage → App: battery
//         status: response.data.device_status === 'online' ? 'active' :     // Value transformation
//                 response.data.device_status === 'offline' ? 'inactive' : 'maintenance',
//         lastUpdate: new Date(response.data.last_update_iso),        // String → Date
//         lastMaintenance: new Date(response.data.last_maintenance_iso), // String → Date
//         isActive: response.data.is_active,
//         color: response.data.color_hex              // API: color_hex → App: color
//       };

//       return tracker;
//     } catch (error) {
//       logger.error('TrackerService: Failed to fetch tracker', error as Error, { trackerId });
//       throw error;
//     }
//   }

//   /**
//    * Update tracker location
//    * Uses direct casting for performance
//    */
//   public async updateTrackerLocation(trackerId: string, location: {
//     lat: number;
//     lng: number;
//   }): Promise<Tracker> {
//     try {
//       logger.info('TrackerService: Updating tracker location', { trackerId, location });

//       const response = await fetchy.patch<Tracker>(`/trackers/${trackerId}/location`, {
//         location: {
//           ...location,
//           timestamp: new Date()
//         }
//       });

//       return response.data;
//     } catch (error) {
//       logger.error('TrackerService: Failed to update tracker location', error as Error, { trackerId });
//       throw error;
//     }
//   }

//   /**
//    * Create a new tracker
//    * Uses direct casting for performance
//    */
//   public async createTracker(trackerData: Omit<Tracker, 'id' | 'lastUpdate'>): Promise<Tracker> {
//     try {
//       logger.info('TrackerService: Creating new tracker', { trackerData });

//       const response = await fetchy.post<Tracker>('/trackers', {
//         ...trackerData,
//         lastUpdate: new Date()
//       });

//       logger.info('TrackerService: Tracker created successfully', { trackerId: response.data.id });

//       return response.data;
//     } catch (error) {
//       logger.error('TrackerService: Failed to create tracker', error as Error);
//       throw error;
//     }
//   }

//   /**
//    * Apply filter criteria to tracker array
//    */
//   private applyFilters(trackers: Tracker[], criteria: FilterCriteria): Tracker[] {
//     return trackers.filter(tracker => this.shouldIncludeTracker(tracker, criteria));
//   }

//   /**
//    * Check if a tracker matches the filter criteria
//    */
//   private shouldIncludeTracker(tracker: Tracker, criteria: FilterCriteria): boolean {
//     // Vehicle type filter
//     if (criteria.vehicleTypes && criteria.vehicleTypes.length > 0) {
//       if (!criteria.vehicleTypes.includes(tracker.type)) {
//         return false;
//       }
//     }

//     // Status filter
//     if (criteria.statuses && criteria.statuses.length > 0 && criteria.statuses.length !== 3) {
//       if (!criteria.statuses.includes(tracker.status)) {
//         return false;
//       }
//     }

//     // Battery range filter
//     if (criteria.batteryRange) {
//       const { min, max } = criteria.batteryRange;
//       if (min > 0 || max < 100) {
//         if (tracker.battery < min || tracker.battery > max) {
//           return false;
//         }
//       }
//     }

//     // Speed range filter
//     if (criteria.speedRange) {
//       const { min, max } = criteria.speedRange;
//       if (min > 0 || max < 500) {
//         if (tracker.speed < min || tracker.speed > max) {
//           return false;
//         }
//       }
//     }

//     // Battery level filter
//     if (criteria.batteryLevels && criteria.batteryLevels.length > 0 && criteria.batteryLevels.length !== 3) {
//       const batteryLevel = tracker.battery <= 20 ? 'low' :
//                           tracker.battery <= 70 ? 'medium' : 'high';
//       if (!criteria.batteryLevels.includes(batteryLevel)) {
//         return false;
//       }
//     }

//     // Name search filter
//     if (criteria.nameSearch) {
//       if (!tracker.name.toLowerCase().includes(criteria.nameSearch.toLowerCase())) {
//         return false;
//       }
//     }

//     // Geographic bounds filter
//     if (criteria.regionBounds) {
//       const { north, south, east, west } = criteria.regionBounds;
//       if (tracker.location.lat < south || tracker.location.lat > north ||
//           tracker.location.lng < west || tracker.location.lng > east) {
//         return false;
//       }
//     }

//     // Last active filter
//     if (criteria.lastActiveAfter) {
//       if (tracker.lastUpdate < criteria.lastActiveAfter) {
//         return false;
//       }
//     }

//     return true;
//   }
// }

// // Export singleton instance
// export const trackerService = TrackerService.getInstance();
