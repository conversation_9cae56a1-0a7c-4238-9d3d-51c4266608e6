import { fetchy } from '@/shared/lib/Fetchy';

import { LocalStorage } from '../local-storage/local-storage';
import { LocalStorageKeys } from '../local-storage/local-storage-keys';

fetchy.addRequestInterceptor((config) => {
    const authToken: string | null = LocalStorage.get(LocalStorageKeys.authToken);

    if (authToken == null || authToken?.length == 0) return config;
    config.headers.Authorization = `Bearer ${authToken}`;

    return config;
});
