// import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
// import { logger } from '../logging/logger';
// import { signalrSimulator } from './signalrSimulator';
// import { Tracker, TrackerUpdate, SignalRMessage } from '../../shared/types';

// export interface SignalREnhancementConfig {
//   useProductionHub: boolean;
//   hubUrl: string;
//   fallbackToSimulation: boolean;
//   reconnectDelays: number[];
//   enableHeartbeat: boolean;
// }

// /**
//  * Enhanced SignalR Manager that works with your existing architecture
//  * - Uses your existing signalrSimulator for demo/development
//  * - Adds production .NET hub integration when ready
//  * - Seamless switching between simulation and real hub
//  */
// export class EnhancedSignalRManager {
//   private static instance: EnhancedSignalRManager;
//   private connection: HubConnection | null = null;
//   private config: SignalREnhancementConfig;
//   private isUsingProduction = false;
//   private subscribers = new Set<(message: SignalRMessage) => void>();

//   private constructor(config: SignalREnhancementConfig = {
//     useProductionHub: false, // Start with simulation
//     hubUrl: 'https://your-dotnet-api.com/trackerhub',
//     fallbackToSimulation: true,
//     reconnectDelays: [0, 2000, 10000, 30000],
//     enableHeartbeat: true
//   }) {
//     this.config = config;
//   }

//   public static getInstance(config?: SignalREnhancementConfig): EnhancedSignalRManager {
//     if (!EnhancedSignalRManager.instance) {
//       EnhancedSignalRManager.instance = new EnhancedSignalRManager(config);
//     }
//     return EnhancedSignalRManager.instance;
//   }

//   /**
//    * Enhanced start that can use either simulation or production hub
//    */
//   public async start(): Promise<void> {
//     logger.info('EnhancedSignalR: Starting', {
//       useProduction: this.config.useProductionHub
//     });

//     if (this.config.useProductionHub) {
//       try {
//         await this.connectToProductionHub();
//         this.isUsingProduction = true;
//       } catch (error) {
//         logger.error('EnhancedSignalR: Production hub failed', error as Error);

//         if (this.config.fallbackToSimulation) {
//           logger.info('EnhancedSignalR: Falling back to simulation');
//           await this.startSimulation();
//           this.isUsingProduction = false;
//         } else {
//           throw error;
//         }
//       }
//     } else {
//       await this.startSimulation();
//       this.isUsingProduction = false;
//     }
//   }

//   /**
//    * Connect to your .NET SignalR Hub
//    */
//   private async connectToProductionHub(): Promise<void> {
//     const builder = new HubConnectionBuilder()
//       .withUrl(this.config.hubUrl, {
//         // Add authentication headers for your .NET backend
//         accessTokenFactory: () => this.getAuthToken()
//       })
//       .withAutomaticReconnect(this.config.reconnectDelays)
//       .configureLogging(LogLevel.Information);

//     this.connection = builder.build();

//     // Set up your .NET hub method handlers
//     this.setupProductionHubHandlers();
//     this.setupConnectionHandlers();

//     await this.connection.start();

//     // Join groups on your .NET hub
//     await this.joinTrackerGroup();

//     logger.info('EnhancedSignalR: Connected to production .NET hub');
//   }

//   /**
//    * Set up handlers for your .NET SignalR Hub methods
//    */
//   private setupProductionHubHandlers(): void {
//     if (!this.connection) return;

//     // Your .NET hub method: TrackerLocationUpdated
//     this.connection.on('TrackerLocationUpdated', (update: TrackerUpdate) => {
//       const message: SignalRMessage = {
//         type: 'TRACKER_UPDATE',
//         data: update,
//         timestamp: new Date()
//       };
//       this.broadcastToSubscribers(message);
//     });

//     // Your .NET hub method: TrackerStatusChanged
//     this.connection.on('TrackerStatusChanged', (trackerId: string, status: string) => {
//       const message: SignalRMessage = {
//         type: 'TRACKER_STATUS',
//         data: { trackerId, status },
//         timestamp: new Date()
//       };
//       this.broadcastToSubscribers(message);
//     });

//     // Your .NET hub method: BulkTrackerUpdate
//     this.connection.on('BulkTrackerUpdate', (updates: TrackerUpdate[]) => {
//       updates.forEach(update => {
//         const message: SignalRMessage = {
//           type: 'TRACKER_UPDATE',
//           data: update,
//           timestamp: new Date()
//         };
//         this.broadcastToSubscribers(message);
//       });
//     });

//     // Your .NET hub method: SystemNotification
//     this.connection.on('SystemNotification', (type: string, data: any) => {
//       const message: SignalRMessage = {
//         type: 'SYSTEM_MESSAGE',
//         data: { type, ...data },
//         timestamp: new Date()
//       };
//       this.broadcastToSubscribers(message);
//     });
//   }

//   private setupConnectionHandlers(): void {
//     if (!this.connection) return;

//     this.connection.onreconnecting(() => {
//       logger.warn('EnhancedSignalR: Reconnecting to .NET hub...');
//       this.notifyConnectionChange('connecting');
//     });

//     this.connection.onreconnected(async () => {
//       logger.info('EnhancedSignalR: Reconnected to .NET hub');
//       await this.joinTrackerGroup();
//       this.notifyConnectionChange('connected');
//     });

//     this.connection.onclose((error) => {
//       logger.warn('EnhancedSignalR: Connection to .NET hub closed', { error: error?.message });
//       this.notifyConnectionChange('disconnected');

//       // Fallback to simulation if enabled
//       if (this.config.fallbackToSimulation && !this.isUsingProduction) {
//         logger.info('EnhancedSignalR: Switching to simulation fallback');
//         this.startSimulation();
//       }
//     });
//   }

//   /**
//    * Start your existing simulation (keeps current demo working)
//    */
//   private async startSimulation(): Promise<void> {
//     logger.info('EnhancedSignalR: Starting simulation mode');

//     // Use your existing signalrSimulator
//     signalrSimulator.start();

//     // Bridge simulation messages to enhanced subscribers
//     signalrSimulator.subscribe((message: SignalRMessage) => {
//       this.broadcastToSubscribers(message);
//     });
//   }

//   /**
//    * Send commands to your .NET hub
//    */
//   public async sendToProductionHub(methodName: string, ...args: any[]): Promise<any> {
//     if (!this.isUsingProduction || !this.connection) {
//       logger.warn('EnhancedSignalR: Not connected to production hub');
//       return;
//     }

//     try {
//       return await this.connection.invoke(methodName, ...args);
//     } catch (error) {
//       logger.error('EnhancedSignalR: Failed to invoke hub method', error as Error, { methodName });
//       throw error;
//     }
//   }

//   /**
//    * Call your .NET hub methods
//    */
//   public async requestTrackerUpdates(trackerIds: string[]): Promise<void> {
//     if (this.isUsingProduction) {
//       await this.sendToProductionHub('RequestTrackerUpdates', trackerIds);
//     } else {
//       logger.debug('EnhancedSignalR: Simulation mode - tracker updates automatic');
//     }
//   }

//   public async updateTrackerViaHub(trackerId: string, update: Partial<TrackerUpdate>): Promise<void> {
//     if (this.isUsingProduction) {
//       await this.sendToProductionHub('UpdateTracker', trackerId, update);
//     } else {
//       logger.debug('EnhancedSignalR: Simulation mode - updates via simulator');
//     }
//   }

//   private async joinTrackerGroup(): Promise<void> {
//     if (this.isUsingProduction && this.connection) {
//       try {
//         await this.connection.invoke('JoinTrackerGroup');
//         logger.info('EnhancedSignalR: Joined tracker updates group on .NET hub');
//       } catch (error) {
//         logger.error('EnhancedSignalR: Failed to join tracker group', error as Error);
//       }
//     }
//   }

//   private getAuthToken(): string {
//     // Get JWT token for your .NET backend authentication
//     return localStorage.getItem('auth_token') || '';
//   }

//   private broadcastToSubscribers(message: SignalRMessage): void {
//     this.subscribers.forEach(callback => {
//       try {
//         callback(message);
//       } catch (error) {
//         logger.error('EnhancedSignalR: Subscriber error', error as Error);
//       }
//     });
//   }

//   private notifyConnectionChange(status: 'connected' | 'disconnected' | 'connecting'): void {
//     const message: SignalRMessage = {
//       type: 'SYSTEM_MESSAGE',
//       data: { type: 'CONNECTION_STATUS_CHANGED', status },
//       timestamp: new Date()
//     };
//     this.broadcastToSubscribers(message);
//   }

//   public subscribe(callback: (message: SignalRMessage) => void): () => void {
//     this.subscribers.add(callback);
//     return () => this.subscribers.delete(callback);
//   }

//   /**
//    * Switch between simulation and production seamlessly
//    */
//   public async switchToProduction(hubUrl?: string): Promise<void> {
//     if (hubUrl) {
//       this.config.hubUrl = hubUrl;
//     }

//     // Stop simulation
//     signalrSimulator.stop();

//     // Connect to production
//     this.config.useProductionHub = true;
//     await this.connectToProductionHub();
//     this.isUsingProduction = true;

//     logger.info('EnhancedSignalR: Switched to production mode');
//   }

//   public switchToSimulation(): void {
//     // Disconnect from production
//     if (this.connection) {
//       this.connection.stop();
//       this.connection = null;
//     }

//     // Start simulation
//     this.config.useProductionHub = false;
//     this.startSimulation();
//     this.isUsingProduction = false;

//     logger.info('EnhancedSignalR: Switched to simulation mode');
//   }

//   public getStatus(): {
//     mode: 'production' | 'simulation';
//     connected: boolean;
//     hubUrl: string;
//   } {
//     return {
//       mode: this.isUsingProduction ? 'production' : 'simulation',
//       connected: this.isUsingProduction ?
//         this.connection?.state === 'Connected' :
//         true, // Simulation is always "connected"
//       hubUrl: this.config.hubUrl
//     };
//   }

//   public async stop(): Promise<void> {
//     if (this.connection) {
//       await this.connection.stop();
//     }
//     signalrSimulator.stop();
//     logger.info('EnhancedSignalR: Stopped');
//   }
// }

// // Export enhanced SignalR manager
// export const enhancedSignalR = EnhancedSignalRManager.getInstance();
