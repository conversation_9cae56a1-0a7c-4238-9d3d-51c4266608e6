// import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
// import { logger } from '../logging/logger';
// import { Tracker, TrackerUpdate, SignalRMessage } from '../../shared/types';

// export interface SignalRConfig {
//   hubUrl: string;
//   automaticReconnect: boolean;
//   reconnectDelays: number[];
//   enableMessagePack: boolean;
// }

// export class SignalRManager {
//   private static instance: SignalRManager;
//   private connection: HubConnection | null = null;
//   private config: SignalRConfig;
//   private subscribers = new Set<(message: SignalRMessage) => void>();
//   private connectionState: 'connected' | 'disconnected' | 'connecting' | 'reconnecting' = 'disconnected';

//   private constructor(config: SignalRConfig = {
//     hubUrl: 'http://localhost:5000/trackerhub', // Your .NET backend
//     automaticReconnect: true,
//     reconnectDelays: [0, 2000, 10000, 30000],
//     enableMessagePack: false
//   }) {
//     this.config = config;
//   }

//   public static getInstance(config?: SignalRConfig): SignalRManager {
//     if (!SignalRManager.instance) {
//       SignalRManager.instance = new SignalRManager(config);
//     }
//     return SignalRManager.instance;
//   }

//   /**
//    * Connect to your .NET SignalR Hub
//    */
//   public async connect(): Promise<void> {
//     if (this.connection?.state === 'Connected') {
//       logger.warn('SignalRManager: Already connected');
//       return;
//     }

//     try {
//       this.connectionState = 'connecting';
//       this.notifyConnectionChange();

//       // Build connection with .NET backend
//       const builder = new HubConnectionBuilder()
//         .withUrl(this.config.hubUrl)
//         .withAutomaticReconnect(this.config.reconnectDelays)
//         .configureLogging(LogLevel.Information);

//       // Add MessagePack if enabled (better performance)
//       if (this.config.enableMessagePack) {
//         // builder.withHubProtocol(new MessagePackHubProtocol());
//       }

//       this.connection = builder.build();

//       // Set up event handlers for your .NET SignalR Hub
//       this.setupHubHandlers();
//       this.setupConnectionHandlers();

//       await this.connection.start();

//       this.connectionState = 'connected';
//       this.notifyConnectionChange();

//       logger.info('SignalRManager: Connected to .NET SignalR Hub', {
//         hubUrl: this.config.hubUrl
//       });

//       // Join tracker updates group (if your .NET hub uses groups)
//       await this.joinTrackerGroup();

//     } catch (error) {
//       this.connectionState = 'disconnected';
//       this.notifyConnectionChange();
//       logger.error('SignalRManager: Connection failed', error as Error);
//       throw error;
//     }
//   }

//   /**
//    * Set up handlers for your .NET SignalR Hub methods
//    */
//   private setupHubHandlers(): void {
//     if (!this.connection) return;

//     // Listen for tracker updates from .NET backend
//     this.connection.on('TrackerLocationUpdated', (update: TrackerUpdate) => {
//       logger.debug('SignalRManager: Received tracker update', { trackerId: update.trackerId });

//       const message: SignalRMessage = {
//         type: 'TRACKER_UPDATE',
//         data: update,
//         timestamp: new Date()
//       };

//       this.broadcast(message);
//     });

//     // Listen for tracker status changes
//     this.connection.on('TrackerStatusChanged', (trackerId: string, status: string) => {
//       logger.debug('SignalRManager: Received status change', { trackerId, status });

//       const message: SignalRMessage = {
//         type: 'TRACKER_STATUS',
//         data: { trackerId, status },
//         timestamp: new Date()
//       };

//       this.broadcast(message);
//     });

//     // Listen for multiple tracker updates (bulk)
//     this.connection.on('BulkTrackerUpdate', (updates: TrackerUpdate[]) => {
//       logger.debug('SignalRManager: Received bulk update', { count: updates.length });

//       updates.forEach(update => {
//         const message: SignalRMessage = {
//           type: 'TRACKER_UPDATE',
//           data: update,
//           timestamp: new Date()
//         };
//         this.broadcast(message);
//       });
//     });

//     // Listen for system messages from .NET backend
//     this.connection.on('SystemMessage', (messageType: string, data: any) => {
//       logger.info('SignalRManager: Received system message', { messageType, data });

//       const message: SignalRMessage = {
//         type: 'SYSTEM_MESSAGE',
//         data: { type: messageType, ...data },
//         timestamp: new Date()
//       };

//       this.broadcast(message);
//     });
//   }

//   private setupConnectionHandlers(): void {
//     if (!this.connection) return;

//     this.connection.onreconnecting((error) => {
//       this.connectionState = 'reconnecting';
//       this.notifyConnectionChange();
//       logger.warn('SignalRManager: Reconnecting...', { error: error?.message });
//     });

//     this.connection.onreconnected((connectionId) => {
//       this.connectionState = 'connected';
//       this.notifyConnectionChange();
//       logger.info('SignalRManager: Reconnected', { connectionId });

//       // Rejoin groups after reconnection
//       this.joinTrackerGroup();
//     });

//     this.connection.onclose((error) => {
//       this.connectionState = 'disconnected';
//       this.notifyConnectionChange();
//       logger.warn('SignalRManager: Connection closed', { error: error?.message });
//     });
//   }

//   /**
//    * Join tracker updates group on your .NET hub
//    */
//   private async joinTrackerGroup(): Promise<void> {
//     if (!this.connection || this.connection.state !== 'Connected') return;

//     try {
//       // Call .NET hub method to join tracker updates group
//       await this.connection.invoke('JoinTrackerGroup');
//       logger.info('SignalRManager: Joined tracker updates group');
//     } catch (error) {
//       logger.error('SignalRManager: Failed to join tracker group', error as Error);
//     }
//   }

//   /**
//    * Send commands to your .NET SignalR Hub
//    */
//   public async sendToHub(methodName: string, ...args: any[]): Promise<any> {
//     if (!this.connection || this.connection.state !== 'Connected') {
//       throw new Error('SignalR connection not available');
//     }

//     try {
//       logger.debug('SignalRManager: Sending to hub', { methodName, args });
//       return await this.connection.invoke(methodName, ...args);
//     } catch (error) {
//       logger.error('SignalRManager: Failed to send to hub', error as Error, { methodName });
//       throw error;
//     }
//   }

//   /**
//    * Request specific tracker updates from .NET backend
//    */
//   public async requestTrackerUpdates(trackerIds: string[]): Promise<void> {
//     await this.sendToHub('RequestTrackerUpdates', trackerIds);
//   }

//   /**
//    * Update tracker location via SignalR (to .NET backend)
//    */
//   public async updateTrackerLocation(trackerId: string, location: { lat: number; lng: number }): Promise<void> {
//     await this.sendToHub('UpdateTrackerLocation', trackerId, location);
//   }

//   private broadcast(message: SignalRMessage): void {
//     this.subscribers.forEach(callback => {
//       try {
//         callback(message);
//       } catch (error) {
//         logger.error('SignalRManager: Subscriber callback error', error as Error);
//       }
//     });
//   }

//   private notifyConnectionChange(): void {
//     const message: SignalRMessage = {
//       type: 'SYSTEM_MESSAGE',
//       data: {
//         type: 'CONNECTION_STATUS_CHANGED',
//         status: this.connectionState
//       },
//       timestamp: new Date()
//     };
//     this.broadcast(message);
//   }

//   public subscribe(callback: (message: SignalRMessage) => void): () => void {
//     this.subscribers.add(callback);
//     return () => this.subscribers.delete(callback);
//   }

//   public getConnectionState(): string {
//     return this.connectionState;
//   }

//   public async disconnect(): Promise<void> {
//     if (this.connection) {
//       await this.connection.stop();
//       this.connection = null;
//       this.connectionState = 'disconnected';
//       this.notifyConnectionChange();
//       logger.info('SignalRManager: Disconnected');
//     }
//   }
// }

// // Export singleton for .NET SignalR integration
// export const signalRManager = SignalRManager.getInstance();
