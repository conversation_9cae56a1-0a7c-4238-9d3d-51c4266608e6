// import { logger } from '../logging/logger';
// import { validator } from '../validation/validator';
// import { appConfig } from '../../../../../../Advanced Photonics/Code Samples/dashboard samples 29-7/src/shared/config/appSettings';
// import { Tracker, TrackerUpdate } from '../validation/schemas';
// import { TrackerLocation, SignalRMessage } from '../../shared/types';

// // Predefined routes for realistic vehicle movement
// const ROUTE_PATTERNS = [
//   // Route 1: East-West Manhattan
//   [
//     { lat: 40.7128, lng: -74.0060 },
//     { lat: 40.7138, lng: -73.9960 },
//     { lat: 40.7148, lng: -73.9860 },
//     { lat: 40.7158, lng: -73.9760 },
//     { lat: 40.7168, lng: -73.9660 }
//   ],
//   // Route 2: North-South through Central Park
//   [
//     { lat: 40.7831, lng: -73.9712 },
//     { lat: 40.7731, lng: -73.9712 },
//     { lat: 40.7631, lng: -73.9712 },
//     { lat: 40.7531, lng: -73.9712 },
//     { lat: 40.7431, lng: -73.9712 }
//   ],
//   // Route 3: Brooklyn Bridge to Williamsburg
//   [
//     { lat: 40.7061, lng: -73.9969 },
//     { lat: 40.7081, lng: -73.9869 },
//     { lat: 40.7101, lng: -73.9769 },
//     { lat: 40.7121, lng: -73.9669 },
//     { lat: 40.7141, lng: -73.9569 }
//   ],
//   // Route 4: FDR Drive South
//   [
//     { lat: 40.7589, lng: -73.9751 },
//     { lat: 40.7489, lng: -73.9751 },
//     { lat: 40.7389, lng: -73.9751 },
//     { lat: 40.7289, lng: -73.9751 },
//     { lat: 40.7189, lng: -73.9751 }
//   ],
//   // Route 5: Cross-town route
//   [
//     { lat: 40.7505, lng: -73.9934 },
//     { lat: 40.7515, lng: -73.9834 },
//     { lat: 40.7525, lng: -73.9734 },
//     { lat: 40.7535, lng: -73.9634 },
//     { lat: 40.7545, lng: -73.9534 }
//   ]
// ];

// // Vehicle types and their properties for dynamic generation
// const VEHICLE_TYPES = [
//   { type: 'truck', namePrefix: 'Delivery Truck' },
//   { type: 'car', namePrefix: 'Taxi' },
//   { type: 'bus', namePrefix: 'Bus' },
//   { type: 'van', namePrefix: 'Van' },
//   { type: 'car', namePrefix: 'Car' },
//   { type: 'motorcycle', namePrefix: 'Motorcycle' },
//   { type: 'police', namePrefix: 'Police Car' },
//   { type: 'ambulance', namePrefix: 'Ambulance' }
// ];

// // Color palette for trackers
// const TRACKER_COLORS = [
//   '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
//   '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
//   '#AED6F1', '#F1948A', '#D2B4DE', '#A9DFBF', '#F9E79F', '#FAD7A0',
//   '#AEB6BF', '#D5A6BD', '#A3E4D7', '#D1F2EB', '#FCF3CF', '#FADBD8',
//   '#E8DAEF', '#D6EAF8', '#D4EDDA', '#FFF3CD', '#F8D7DA', '#D1ECF1'
// ];

// // Greek alphabet for naming
// const GREEK_LETTERS = [
//   'Alpha', 'Beta', 'Gamma', 'Delta', 'Epsilon', 'Zeta', 'Eta', 'Theta',
//   'Iota', 'Kappa', 'Lambda', 'Mu', 'Nu', 'Xi', 'Omicron', 'Pi',
//   'Rho', 'Sigma', 'Tau', 'Upsilon', 'Phi', 'Chi', 'Psi', 'Omega'
// ];

// // Generate tracker configurations based on app settings
// const generateTrackerConfigs = () => {
//   const numberOfTrackers = appConfig.get('simulation').numberOfTrackers;
//   const trackerConfigs = [];

//   for (let i = 0; i < numberOfTrackers; i++) {
//     const vehicleType = VEHICLE_TYPES[i % VEHICLE_TYPES.length];
//     const routeIndex = i % ROUTE_PATTERNS.length;
//     const color = TRACKER_COLORS[i % TRACKER_COLORS.length];
//     const greekLetter = GREEK_LETTERS[i % GREEK_LETTERS.length];

//     trackerConfigs.push({
//       id: `tracker-${String(i + 1).padStart(3, '0')}`,
//       name: `${vehicleType.namePrefix} ${greekLetter}`,
//       routeIndex,
//       color,
//       vehicleType: vehicleType.type
//     });
//   }

//   return trackerConfigs;
// };

// // Demo tracker configurations with assigned routes (generated from app settings)
// const DEMO_TRACKERS_CONFIG = generateTrackerConfigs();

// interface TrackerSimulationState {
//   currentLocation: TrackerLocation;
//   routePoints: TrackerLocation[];
//   currentRouteIndex: number;
//   speed: number;
//   baseSpeed: number; // Vehicle's typical speed
//   battery: number;
//   status: 'active' | 'inactive' | 'maintenance';
//   direction: number; // in degrees
//   lastUpdate: Date;
//   vehicleType: string;
//   isReversing: boolean; // For route completion
// }

// type SignalRCallback = (message: SignalRMessage) => void;

// export class SignalRSimulator {
//   private static instance: SignalRSimulator;
//   private simulationState: Map<string, TrackerSimulationState> = new Map();
//   private intervalId: any = null;
//   private callbacks: SignalRCallback[] = [];
//   private isRunning: boolean = false;
//   private updateInterval: number = appConfig.get('simulation').updateInterval;
//   private connectionStatus: 'connected' | 'disconnected' | 'connecting' = 'disconnected';

//   private constructor() {
//     this.initializeTrackers();
//   }

//   public static getInstance(): SignalRSimulator {
//     if (!SignalRSimulator.instance) {
//       SignalRSimulator.instance = new SignalRSimulator();
//     }
//     return SignalRSimulator.instance;
//   }

//   // Initialize demo trackers with predefined routes
//   private initializeTrackers(): void {
//     DEMO_TRACKERS_CONFIG.forEach(config => {
//       const route = ROUTE_PATTERNS[config.routeIndex];
//       const initialLocation: TrackerLocation = {
//         lat: route[0].lat,
//         lng: route[0].lng,
//         timestamp: new Date()
//       };

//       const baseSpeed = this.getBaseSpeedForVehicleType(config.vehicleType);

//       this.simulationState.set(config.id, {
//         currentLocation: initialLocation,
//         routePoints: route.map(point => ({ ...point, timestamp: new Date() })),
//         currentRouteIndex: 0,
//         speed: baseSpeed,
//         baseSpeed: baseSpeed,
//         battery: Math.floor(Math.random() * 40) + 60, // 60-100%
//         status: 'active',
//         direction: 0,
//         lastUpdate: new Date(),
//         vehicleType: config.vehicleType,
//         isReversing: false
//       });
//     });

//     logger.info('SignalR Simulator: Demo trackers initialized', {
//       count: DEMO_TRACKERS_CONFIG.length
//     });
//   }

//   // Generate initial tracker data for the dashboard
//   public getInitialTrackers(): Tracker[] {
//     // Ensure trackers are initialized
//     if (this.simulationState.size === 0) {
//       this.initializeTrackers();
//     }

//     const trackers: Tracker[] = [];

//     DEMO_TRACKERS_CONFIG.forEach(config => {
//       const state = this.simulationState.get(config.id);
//       if (state) {
//         const tracker: Tracker = {
//           id: config.id,
//           name: config.name,
//           type: state.vehicleType as 'car' | 'truck' | 'bus' | 'van' | 'motorcycle' | 'police' | 'ambulance',
//           location: state.currentLocation,
//           speed: state.speed,
//           status: state.status,
//           battery: state.battery,
//           lastUpdate: state.lastUpdate,
//           lastMaintenance: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
//           isActive: state.status === 'active',
//           color: config.color
//         };

//         // Directly push tracker without validation
//         trackers.push(tracker);
//       }
//     });

//     logger.info('SignalR Simulator: Initial trackers generated', { count: trackers.length });
//     return trackers;
//   }

//   // Start the simulation
//   public start(): void {
//     if (this.isRunning) {
//       logger.warn('SignalR Simulator: Already running');
//       return;
//     }

//     this.connectionStatus = 'connecting';
//     this.notifyConnectionChange();

//     // Simulate connection delay
//     setTimeout(() => {
//       this.connectionStatus = 'connected';
//       this.isRunning = true;
//       this.notifyConnectionChange();

//       this.intervalId = setInterval(() => {
//         this.updateTrackers();
//       }, this.updateInterval);

//       logger.info('SignalR Simulator: Started', { updateInterval: this.updateInterval });
//     }, 1000);
//   }

//   // Stop the simulation
//   public stop(): void {
//     if (!this.isRunning) {
//       logger.warn('SignalR Simulator: Not running');
//       return;
//     }

//     if (this.intervalId) {
//       clearInterval(this.intervalId);
//       this.intervalId = null;
//     }

//     this.isRunning = false;
//     this.connectionStatus = 'disconnected';
//     this.notifyConnectionChange();

//     logger.info('SignalR Simulator: Stopped');
//   }

//   // Subscribe to updates
//   public subscribe(callback: SignalRCallback): () => void {
//     this.callbacks.push(callback);
//     logger.debug('SignalR Simulator: Callback subscribed', { totalCallbacks: this.callbacks.length });

//     // Return unsubscribe function
//     return () => {
//       const index = this.callbacks.indexOf(callback);
//       if (index > -1) {
//         this.callbacks.splice(index, 1);
//         logger.debug('SignalR Simulator: Callback unsubscribed', { totalCallbacks: this.callbacks.length });
//       }
//     };
//   }

//   // Get connection status
//   public getConnectionStatus(): 'connected' | 'disconnected' | 'connecting' {
//     return this.connectionStatus;
//   }

//   // Update all trackers and broadcast changes
//   private updateTrackers(): void {
//     const updates: TrackerUpdate[] = [];

//     this.simulationState.forEach((state, trackerId) => {
//       try {
//         // Update tracker position
//         this.moveTracker(trackerId, state);

//         // Occasionally change status or update battery
//         this.updateTrackerState(state);

//         // Create update message
//         const update: TrackerUpdate = {
//           trackerId,
//           location: { ...state.currentLocation },
//           speed: state.speed,
//           battery: state.battery,
//           status: state.status
//         };

//         updates.push(update);
//       } catch (error) {
//         logger.error('SignalR Simulator: Error updating tracker', error as Error, { trackerId });
//       }
//     });

//     // Broadcast updates
//     if (updates.length > 0) {
//       updates.forEach(update => {
//         const message: SignalRMessage = {
//           type: 'TRACKER_UPDATE',
//           data: update,
//           timestamp: new Date()
//         };

//         this.broadcast(message);
//       });

//       logger.debug('SignalR Simulator: Broadcasted updates', { count: updates.length });
//     }
//   }

//   // Move a tracker along its predefined route
//   private moveTracker(trackerId: string, state: TrackerSimulationState): void {
//     const current = state.currentLocation;

//     // Get next target point on route
//     let targetIndex = state.currentRouteIndex;
//     if (state.isReversing) {
//       targetIndex = state.routePoints.length - 1 - state.currentRouteIndex;
//     }

//     // Check if we've reached the end of the route
//     if (targetIndex >= state.routePoints.length) {
//       state.isReversing = true;
//       state.currentRouteIndex = 0;
//       targetIndex = state.routePoints.length - 1;
//     } else if (state.isReversing && targetIndex < 0) {
//       state.isReversing = false;
//       state.currentRouteIndex = 0;
//       targetIndex = 0;
//     }

//     const target = state.routePoints[targetIndex];

//     // Calculate distance to target
//     const distance = this.calculateDistance(current.lat, current.lng, target.lat, target.lng);

//     // If close to target, move to next point
//     if (distance < 0.0008) { // ~80 meters
//       state.currentRouteIndex++;

//       // Vary speed slightly when reaching waypoints
//       const speedVariation = (Math.random() - 0.5) * 10; // ±5 km/h
//       state.speed = Math.max(5, Math.min(80, state.baseSpeed + speedVariation));

//       return;
//     }

//     // Calculate movement step based on speed (more realistic)
//     const speedInKmPerHour = state.speed;
//     const speedInMetersPerSecond = speedInKmPerHour * 1000 / 3600;
//     const moveDistanceInMeters = speedInMetersPerSecond * (this.updateInterval / 1000);

//     // Convert to degrees (rough approximation: 1 degree ≈ 111 km)
//     const moveDistanceInDegrees = moveDistanceInMeters / (111 * 1000);

//     // Calculate direction
//     const bearing = this.calculateBearing(current.lat, current.lng, target.lat, target.lng);
//     state.direction = bearing;

//     // Calculate new position with smooth movement
//     const bearingRad = bearing * Math.PI / 180;
//     const newLat = current.lat + (moveDistanceInDegrees * Math.cos(bearingRad));
//     const newLng = current.lng + (moveDistanceInDegrees * Math.sin(bearingRad));

//     // Validate new coordinates
//     const coordsValidation = validator.validateCoordinates(newLat, newLng);
//     if (coordsValidation.success) {
//       state.currentLocation = {
//         lat: newLat,
//         lng: newLng,
//         timestamp: new Date()
//       };
//       state.lastUpdate = new Date();
//     }
//   }

//   // Get base speed for vehicle type
//   private getBaseSpeedForVehicleType(vehicleType: string): number {
//     const speedRanges: Record<string, number> = {
//       'truck': 35,        // Delivery trucks - slower
//       'bus': 25,          // City buses - slowest
//       'van': 40,          // Service vans - moderate
//       'car': 45,          // Regular cars - moderate
//       'taxi': 50,         // Taxis - faster
//       'motorcycle': 55,   // Motorcycles - fastest
//       'police': 60,       // Police - fast (can exceed speed limit)
//       'ambulance': 65     // Emergency vehicles - fastest
//     };

//     return speedRanges[vehicleType] || 40; // Default speed
//   }

//   // Update tracker state (battery, status changes, traffic effects)
//   private updateTrackerState(state: TrackerSimulationState): void {
//     // Apply realistic traffic effects
//     this.applyTrafficEffects(state);

//     // Battery consumption based on vehicle type and speed
//     const consumptionRate = this.getBatteryConsumptionRate(state.vehicleType, state.speed);
//     if (Math.random() < consumptionRate) {
//       state.battery = Math.max(0, state.battery - 1);
//     }

//     // Vehicle-specific status changes
//     if (Math.random() < 0.001) { // 0.1% chance each update
//       const newStatus = this.getRandomStatusForVehicleType(state.vehicleType, state.status);

//       if (newStatus !== state.status) {
//         state.status = newStatus;
//         logger.info('SignalR Simulator: Tracker status changed', {
//           vehicleType: state.vehicleType,
//           status: newStatus
//         });
//       }
//     }

//     // Recharge/refuel based on vehicle type
//     if (state.battery < 20 && Math.random() < this.getRefuelProbability(state.vehicleType)) {
//       state.battery = Math.min(100, state.battery + this.getRefuelAmount(state.vehicleType));
//     }
//   }

//   // Get battery consumption rate based on vehicle type and speed
//   private getBatteryConsumptionRate(vehicleType: string, speed: number): number {
//     const baseRates: Record<string, number> = {
//       'truck': 0.15,      // Trucks consume more
//       'bus': 0.20,        // Buses consume most
//       'van': 0.12,
//       'car': 0.08,        // Cars are efficient
//       'taxi': 0.10,       // Taxis run more
//       'motorcycle': 0.05, // Motorcycles are most efficient
//       'police': 0.10,
//       'ambulance': 0.12
//     };

//     const baseRate = baseRates[vehicleType] || 0.10;
//     // Higher speeds consume more battery
//     return baseRate * (1 + speed / 100);
//   }

//   // Get random status based on vehicle type
//   private getRandomStatusForVehicleType(vehicleType: string, currentStatus: string): 'active' | 'inactive' | 'maintenance' {
//     // Emergency vehicles rarely go inactive
//     if (vehicleType === 'ambulance' || vehicleType === 'police') {
//       const statuses: Array<'active' | 'maintenance'> = ['active', 'maintenance'];
//       return statuses[Math.floor(Math.random() * statuses.length)];
//     }

//     // Regular vehicles
//     const statuses: Array<'active' | 'inactive' | 'maintenance'> = ['active', 'inactive', 'maintenance'];
//     return statuses[Math.floor(Math.random() * statuses.length)];
//   }

//   // Get refuel probability based on vehicle type
//   private getRefuelProbability(vehicleType: string): number {
//     const probabilities: Record<string, number> = {
//       'truck': 0.08,      // Trucks refuel regularly
//       'bus': 0.10,        // Buses have scheduled refueling
//       'van': 0.06,
//       'car': 0.04,
//       'taxi': 0.08,       // Taxis refuel frequently
//       'motorcycle': 0.03,
//       'police': 0.08,     // Regular service
//       'ambulance': 0.10   // Priority refueling
//     };

//     return probabilities[vehicleType] || 0.05;
//   }

//   // Get refuel amount based on vehicle type
//   private getRefuelAmount(vehicleType: string): number {
//     const amounts: Record<string, number> = {
//       'truck': 15,        // Trucks refuel more
//       'bus': 20,          // Buses get full refuel
//       'van': 12,
//       'car': 10,
//       'taxi': 15,         // Taxis get substantial refuel
//       'motorcycle': 8,
//       'police': 15,
//       'ambulance': 20     // Emergency vehicles get priority
//     };

//     return amounts[vehicleType] || 10;
//   }

//   // Add some realistic traffic behavior
//   private applyTrafficEffects(state: TrackerSimulationState): void {
//     // Simulate traffic conditions affecting speed
//     if (Math.random() < 0.1) { // 10% chance of traffic effects
//       const trafficFactor = Math.random() * 0.7 + 0.3; // 30-100% of base speed
//       state.speed = Math.max(5, state.baseSpeed * trafficFactor);
//     } else if (Math.random() < 0.05) { // 5% chance of clear roads
//       state.speed = Math.min(state.baseSpeed * 1.2, 80); // Up to 20% faster
//     }

//     // Emergency vehicles can go faster in emergencies
//     if ((state.vehicleType === 'ambulance' || state.vehicleType === 'police') && Math.random() < 0.1) {
//       state.speed = Math.min(state.baseSpeed * 1.5, 90); // Emergency response
//     }
//   }

//   // Calculate distance between two points (Haversine formula)
//   private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
//     const R = 6371; // Earth's radius in km
//     const dLat = (lat2 - lat1) * Math.PI / 180;
//     const dLng = (lng2 - lng1) * Math.PI / 180;
//     const a =
//       Math.sin(dLat/2) * Math.sin(dLat/2) +
//       Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
//       Math.sin(dLng/2) * Math.sin(dLng/2);
//     const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
//     return R * c;
//   }

//   // Calculate bearing between two points
//   private calculateBearing(lat1: number, lng1: number, lat2: number, lng2: number): number {
//     const dLng = (lng2 - lng1) * Math.PI / 180;
//     const y = Math.sin(dLng) * Math.cos(lat2 * Math.PI / 180);
//     const x = Math.cos(lat1 * Math.PI / 180) * Math.sin(lat2 * Math.PI / 180) -
//               Math.sin(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.cos(dLng);

//     const bearing = Math.atan2(y, x) * 180 / Math.PI;
//     return (bearing + 360) % 360;
//   }

//   // Broadcast message to all subscribers
//   private broadcast(message: SignalRMessage): void {
//     this.callbacks.forEach(callback => {
//       try {
//         callback(message);
//       } catch (error) {
//         logger.error('SignalR Simulator: Error in callback', error as Error);
//       }
//     });
//   }

//   // Notify connection status change
//   private notifyConnectionChange(): void {
//     const message: SignalRMessage = {
//       type: 'SYSTEM_MESSAGE',
//       data: {
//         type: 'CONNECTION_STATUS_CHANGED',
//         status: this.connectionStatus
//       },
//       timestamp: new Date()
//     };

//     this.broadcast(message);
//     logger.info('SignalR Simulator: Connection status changed', { status: this.connectionStatus });
//   }

//   // Configuration methods
//   public setUpdateInterval(interval: number): void {
//     if (interval < 500 || interval > 10000) {
//       logger.warn('SignalR Simulator: Invalid update interval', { interval });
//       return;
//     }

//     this.updateInterval = interval;

//     // Restart with new interval if running
//     if (this.isRunning) {
//       this.stop();
//       setTimeout(() => this.start(), 100);
//     }

//     logger.info('SignalR Simulator: Update interval changed', { interval });
//   }

//   public getUpdateInterval(): number {
//     return this.updateInterval;
//   }

//   // Get current tracker states for debugging
//   public getTrackerStates(): Map<string, TrackerSimulationState> {
//     return new Map(this.simulationState);
//   }
// }

// // Export singleton instance
// export const signalrSimulator = SignalRSimulator.getInstance();
