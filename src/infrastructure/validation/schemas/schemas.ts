// import { z } from 'zod';
// import { logger } from '../logging/logger';

// // Base validation utilities
// export const createLocationSchema = () => z.object({
//   lat: z.number().min(-90).max(90, 'Latitude must be between -90 and 90'),
//   lng: z.number().min(-180).max(180, 'Longitude must be between -180 and 180'),
//   timestamp: z.date()
// });

// // Authentication schemas
// export const loginCredentialsSchema = z.object({
//   username: z.string()
//     .min(3, 'Username must be at least 3 characters')
//     .max(50, 'Username must not exceed 50 characters')
//     .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
//   password: z.string()
//     .min(6, 'Password must be at least 6 characters')
//     .max(100, 'Password must not exceed 100 characters')
// });

// export const userSchema = z.object({
//   id: z.string().uuid('Invalid user ID format'),
//   username: z.string().min(3).max(50),
//   email: z.string().email('Invalid email format'),
//   token: z.string().min(1, 'Token is required')
// });

// export const authResponseSchema = z.object({
//   user: userSchema,
//   token: z.string().min(1, 'Token is required'),
//   refreshToken: z.string().min(1, 'Refresh token is required')
// });

// // Tracker schemas
// export const trackerLocationSchema = createLocationSchema();

// export const trackerSchema = z.object({
//   id: z.string().min(1, 'Tracker ID is required').regex(/^(tracker-\d+|[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|[\w-]+-tracker-\d+)$/i, 'Invalid tracker ID format'),
//   name: z.string()
//     .min(3, 'Tracker name is required')
//     .max(100, 'Tracker name must not exceed 100 characters')
//     .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Tracker name contains invalid characters'),
//   type: z.enum(['car', 'truck', 'bus', 'van', 'motorcycle', 'police', 'ambulance']),
//   location: trackerLocationSchema,
//   speed: z.number()
//     .min(0, 'Speed cannot be negative')
//     .max(500, 'Speed seems unrealistic (max 500 km/h)'),
//   status: z.enum(['active', 'inactive', 'maintenance'], {
//     errorMap: () => ({ message: 'Status must be active, inactive, or maintenance' })
//   }),
//   battery: z.number()
//     .min(0, 'Battery percentage cannot be negative')
//     .max(100, 'Battery percentage cannot exceed 100'),
//   lastUpdate: z.date(),
//   lastMaintenance: z.date(),
//   isActive: z.boolean(),
//   color: z.string()
//     .regex(/^#[0-9A-Fa-f]{6}$/, 'Color must be a valid hex color code')
// });

// export const trackerUpdateFormSchema = trackerSchema.pick({
//   id: true,
//   name: true,
//   type: true,
//   speed: true,
//   status: true,
//   battery: true,
//   lastMaintenance: true,
//   isActive: true,
//   color: true
// });

// export const trackerUpdateSchema = z.object({
//   trackerId: z.string().min(1, 'Tracker ID is required').regex(/^(tracker-\d+|[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|[\w-]+-tracker-\d+)$/i, 'Invalid tracker ID format'),
//   location: trackerLocationSchema,
//   speed: z.number().min(0).max(500),
//   battery: z.number().min(0).max(100),
//   status: z.enum(['active', 'inactive', 'maintenance'])
// });

// // Dashboard schemas
// export const dashboardStateSchema = z.object({
//   trackers: z.array(trackerSchema),
//   selectedTracker: z.string().min(1).regex(/^(tracker-\d+|[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|[\w-]+-tracker-\d+)$/i).nullable(),
//   viewCenter: trackerLocationSchema,
//   zoom: z.number()
//     .min(1, 'Zoom level too low')
//     .max(20, 'Zoom level too high'),
//   isLoading: z.boolean(),
//   error: z.string().nullable()
// });

// // API schemas
// export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) => z.object({
//   data: dataSchema,
//   success: z.boolean(),
//   message: z.string().optional(),
//   timestamp: z.date()
// });

// export const apiErrorSchema = z.object({
//   message: z.string().min(1, 'Error message is required'),
//   code: z.string().min(1, 'Error code is required'),
//   details: z.any().optional()
// });

// // SignalR schemas
// export const signalRMessageSchema = z.object({
//   type: z.enum(['TRACKER_UPDATE', 'TRACKER_STATUS', 'SYSTEM_MESSAGE']),
//   data: z.any(),
//   timestamp: z.date()
// });

// // Configuration schemas
// export const fetchyConfigSchema = z.object({
//   baseURL: z.string().url('Base URL must be a valid URL'),
//   timeout: z.number()
//     .min(1000, 'Timeout must be at least 1 second')
//     .max(60000, 'Timeout cannot exceed 60 seconds'),
//   retries: z.number()
//     .min(0, 'Retries cannot be negative')
//     .max(5, 'Maximum 5 retries allowed'),
//   retryDelay: z.number()
//     .min(100, 'Retry delay must be at least 100ms')
//     .max(10000, 'Retry delay cannot exceed 10 seconds')
// });

// // Export schema types for TypeScript inference
// export type LoginCredentials = z.infer<typeof loginCredentialsSchema>;
// export type User = z.infer<typeof userSchema>;
// export type AuthResponse = z.infer<typeof authResponseSchema>;
// export type TrackerLocation = z.infer<typeof trackerLocationSchema>;
// export type Tracker = z.infer<typeof trackerSchema>;
// export type TrackerUpdate = z.infer<typeof trackerUpdateSchema>;
// export type DashboardState = z.infer<typeof dashboardStateSchema>;
// export type ApiError = z.infer<typeof apiErrorSchema>;
// export type SignalRMessage = z.infer<typeof signalRMessageSchema>;
// export type FetchyConfig = z.infer<typeof fetchyConfigSchema>;
