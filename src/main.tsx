import '@/assets/styles/variables.css';
import 'primereact/resources/themes/lara-light-cyan/theme.css';
import { StrictMode, Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import '@/infrastructure/interceptors';

import App from './app/App.tsx';
import './index.css';
import './shared/config/i18n.ts'; // Initialize i18n
import { initializeLocalizationStore } from './stores/localizationStore';
import PageLoader from './components/common/page-loader/PageLoader.tsx';

// Initialize the localization store
initializeLocalizationStore();

createRoot(document.getElementById('root') as HTMLElement).render(
    <Suspense fallback={<PageLoader />}>
        <StrictMode>
            <App />
        </StrictMode>
    </Suspense>,
);
