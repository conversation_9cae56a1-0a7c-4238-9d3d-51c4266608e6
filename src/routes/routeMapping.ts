/**
 * Route mapping configuration for breadcrumb navigation
 * Maps route paths to user-friendly display names
 */
export interface RouteMapping {
    [key: string]: string;
}

/**
 * Default route mapping for the application
 * Add more routes as needed for your application
 */
export const defaultRouteMap: RouteMapping = {
    '/': 'Home',
    '/my-ports': 'My Ports',
    '/monitor-board': 'Monitor Board',
    '/alerts': 'Alerts',
    '/settings': 'Settings',
    '/settings/user': 'User',
    '/settings/user/profile': 'Profile',
    '/products': 'Products',
    '/products/categories': 'Categories',
    '/trips': 'Trips',
    '/ports': 'Ports',
    '/login': 'Login',
};

/**
 * Get user-friendly name for a route path
 * @param path - The route path
 * @param routeMap - Optional custom route mapping
 * @returns User-friendly name for the route
 */
export const getRouteName = (path: string, routeMap: RouteMapping = defaultRouteMap): string => {
    // Check for exact match first
    if (routeMap[path]) {
        return routeMap[path];
    }

    // If no exact match, try to find the closest match
    const segments = path.split('/').filter(Boolean);
    if (segments.length === 0) {
        return routeMap['/'] || 'Home';
    }

    // Try to build the path progressively
    let currentPath = '';
    for (const segment of segments) {
        currentPath += `/${segment}`;
        if (routeMap[currentPath]) {
            return routeMap[currentPath];
        }
    }

    // If no mapping found, capitalize the last segment
    const lastSegment = segments[segments.length - 1];
    return lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1).replace(/-/g, ' ');
};
