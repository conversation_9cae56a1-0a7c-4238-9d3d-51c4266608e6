import type { AxiosError } from 'axios';
import axios, { type AxiosInstance } from 'axios';

import type { ILogger } from '@/infrastructure/logging';

import type {
    FetchyConfig,
    FetchyOptions,
    FetchyResponse,
    onInterceptorFulfilled,
    onInterceptorRejected,
} from './types';
import type { IFetchy } from './ifetchy';
import { DEFAULT_CONFIG } from './utils/defaultOptions';
import { QueryClientWrapper } from './utils/queryClient';

export class Fetchy implements IFetchy {
    private static instance: Fetchy;
    private config: FetchyConfig;
    public queryClient: QueryClientWrapper;
    private logger: ILogger | null;
    private httpClient: AxiosInstance;

    private constructor(config: Partial<FetchyConfig> = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.logger = this.config.Logger || null;
        this.queryClient = new QueryClientWrapper(this.config, this.logger);

        this.httpClient = axios.create({
            baseURL: this.config.baseURL,
            timeout: this.config.timeout,
            headers: this.config.headers,
        });

        this.logger?.info('Fetchy initialized', { config: this.config });
    }

    public static getInstance(config?: Partial<FetchyConfig>): Fetchy {
        if (!Fetchy.instance) {
            Fetchy.instance = new Fetchy(config);
        } else if (config) {
            // Allow reconfiguration of existing instance
            Fetchy.instance.updateConfig(config);
        }
        return Fetchy.instance;
    }

    // Core HTTP request method
    private async makeRequest<T>(endpoint: string, options: FetchyOptions = {}): Promise<FetchyResponse<T>> {
        // Log the request
        this.logger?.logApiRequest(options.method || 'GET', endpoint, options.body);

        try {
            const response = await this.httpClient.request({
                url: endpoint,
                method: options.method || 'GET',
                params: options.params,
                headers: options.headers,
                data: options.body,
                timeout: options.timeout || this.config.timeout,
            });
            return {
                data: response.data as T,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers as Record<string, string>,
            };
        } catch (error) {
            debugger;

            const axiosError = error as AxiosError;
            // Handle different types of errors
            if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
                return {
                    data: {
                        message: 'Request timeout',
                        code: 'TIMEOUT',
                        details: { timeout: options.timeout || this.config.timeout },
                    } as T,
                    status: 408,
                    statusText: 'Request Timeout',
                    headers: {},
                } as FetchyResponse<T>;
            }

            // Handle other axios errors
            return {
                data: {
                    message: axiosError.response?.data || axiosError.message || 'Request failed',
                    code: axiosError.response?.status?.toString() || 'UNKNOWN',
                    details: axiosError.response?.data || {},
                } as T,
                status: axiosError.response?.status || 500,
                statusText: axiosError.response?.statusText || 'Internal Server Error',
                headers: axiosError.response?.headers || {},
            } as FetchyResponse<T>;
        }
    }

    // Public HTTP methods
    public async get<T>(
        endpoint: string,
        options?: Omit<FetchyOptions, 'method' | 'body'> & { params?: Record<string, unknown> },
    ): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'GET', params: options?.params });
    }

    public async post<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'POST', body: options?.body });
    }

    public async put<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'PUT', body: options?.body });
    }

    public async patch<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'PATCH', body: options?.body });
    }

    public async delete<T>(
        endpoint: string,
        options?: Omit<FetchyOptions, 'method' | 'body'>,
    ): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' });
    }

    // Configuration methods
    public updateConfig(newConfig: Partial<FetchyConfig>): void {
        const oldConfig = { ...this.config };
        this.config = { ...this.config, ...newConfig };

        // Update logger reference if provided
        this.logger = this.config.Logger || null;

        // Recreate QueryClient if retry configuration changed
        if (oldConfig.retries !== this.config.retries || oldConfig.retryDelay !== this.config.retryDelay) {
            this.queryClient = new QueryClientWrapper(this.config, this.logger);
        }

        this.logger?.info('Fetchy configuration updated', {
            oldConfig,
            newConfig: this.config,
        });
    }

    public setLocaleHeader(locale: string): void {
        this.httpClient.defaults.headers.common['Accept-Language'] = locale;
    }

    public setAuthHeader(token: string): void {
        this.httpClient.defaults.headers.common['Authorization'] = token;
    }

    public addRequestInterceptor(onFulfilled?: onInterceptorFulfilled, onRejected?: onInterceptorRejected): number {
        return this.httpClient.interceptors.request.use(onFulfilled, onRejected);
    }

    public getConfig(): FetchyConfig {
        return { ...this.config };
    }
}
