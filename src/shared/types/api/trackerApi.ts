// // API Response DTOs - What we receive FROM backend
// export interface TrackerApiResponse {
//   id: string;
//   vehicle_name: string;
//   coordinates: {
//     latitude: number;
//     longitude: number;
//     recorded_at: string; // ISO string from API
//   };
//   speed_kmh: number;
//   battery_percentage: number;
//   device_status: 'online' | 'offline' | 'maintenance';
//   vehicle_type: 'car' | 'truck' | 'bus' | 'van' | 'motorcycle' | 'police' | 'ambulance';
//   last_update_iso: string;
//   last_maintenance_iso: string;
//   is_active: boolean;
//   color_hex: string;
// }

// // API Request DTOs - What we send TO backend
// export interface TrackerCreateRequest {
//   vehicleName: string;
//   vehicleType: 'car' | 'truck' | 'bus' | 'van' | 'motorcycle' | 'police' | 'ambulance';
//   initialLocation: {
//     latitude: number;
//     longitude: number;
//   };
//   colorHex: string;
// }

// export interface TrackerUpdateRequest {
//   trackerId: string;
//   location?: {
//     latitude: number;
//     longitude: number;
//   };
//   speed?: number;
//   battery?: number;
//   status?: 'online' | 'offline' | 'maintenance';
// }
