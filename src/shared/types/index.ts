// Validation types
export interface ValidationError {
    field: string;
    message: string;
    code: string;
}

export interface ValidationResult<T> {
    success: boolean;
    data?: T;
    errors?: ValidationError[];
}

// ---------------------------------------------------

// // Authentication types
// export interface User {
//   id: string;
//   username: string;
//   email: string;
//   token: string;
// }

// export interface LoginCredentials {
//   username: string;
//   password: string;
// }

// export interface AuthResponse {
//   user: User;
//   token: string;
//   refreshToken: string;
// }

// // Tracker types
// export interface TrackerLocation {
//   lat: number;
//   lng: number;
//   timestamp: Date;
// }

// export interface Tracker {
//   id: string;
//   name: string;
//   location: TrackerLocation;
//   speed: number; // km/h
//   status: 'active' | 'inactive' | 'maintenance';
//   battery: number; // percentage
//   lastUpdate: Date;
//   color: string; // for map display
// }

// // Dashboard types
// export interface DashboardState {
//   trackers: Tracker[];
//   selectedTracker: string | null;
//   viewCenter: TrackerLocation;
//   zoom: number;
//   isLoading: boolean;
//   error: string | null;
// }

// // API types
// export interface ApiResponse<T> {
//   data: T;
//   success: boolean;
//   message?: string;
//   timestamp: Date;
// }

// export interface ApiError {
//   message: string;
//   code: string;
//   details?: any;
// }

// // SignalR types
// export interface SignalRMessage {
//   type: 'TRACKER_UPDATE' | 'TRACKER_STATUS' | 'SYSTEM_MESSAGE';
//   data: any;
//   timestamp: Date;
// }

// export interface TrackerUpdate {
//   trackerId: string;
//   location: TrackerLocation;
//   speed: number;
//   battery: number;
//   status: Tracker['status'];
// }

// // Logging types
// export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// export interface LogEntry {
//   level: LogLevel;
//   message: string;
//   timestamp: Date;
//   context?: Record<string, any>;
//   error?: Error;
// }

// // Fetchy (Data layer) types
// export interface FetchyConfig {
//   baseURL: string;
//   timeout: number;
//   retries: number;
//   retryDelay: number;
// }

// export interface FetchyOptions {
//   method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
//   headers?: Record<string, string>;
//   body?: any;
//   params?: Record<string, any>;
//   timeout?: number;
// }

// // Store types
// export interface AuthStore {
//   user: User | null;
//   isAuthenticated: boolean;
//   isLoading: boolean;
//   error: string | null;
//   login: (credentials: LoginCredentials) => Promise<void>;
//   logout: () => void;
//   clearError: () => void;
// }

// export interface DashboardStore {
//   state: DashboardState;
//   actions: {
//     setTrackers: (trackers: Tracker[]) => void;
//     updateTracker: (update: TrackerUpdate) => void;
//     selectTracker: (trackerId: string | null) => void;
//     setViewCenter: (location: TrackerLocation) => void;
//     setZoom: (zoom: number) => void;
//     setLoading: (loading: boolean) => void;
//     setError: (error: string | null) => void;
//   };
// }
