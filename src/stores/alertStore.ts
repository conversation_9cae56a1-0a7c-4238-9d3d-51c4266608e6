/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';

export interface Alert {
    tripId: number;
    timeStamp: string;
    alertId: number;
    alertType: {
        id: number;
        name: {
            arabic: string;
            english: string;
        };
    };
    fromStateDateTime: string;
    toStateDateTime: string;
    currentStateOfTrip: {
        id: number;
        locationSource: string;
        batteryLevelPercentage: number;
        chargerStatus: 1 | 2; // 1 = connected, 2 = disconnected
        gpsSignalStrength: number;
        gsmSignalStrength: number;
        currentSpeed: number;
        routeZone: string;
        isWithinRouteGeofence: boolean;
        isWithinSuspiciousZone: boolean;
        remainingDistanceInMeters: number;
        timeElapsedSinceTripStartInMinutes: number;
        trackerDateTime: string;
        dateTime: string;
        tripStatus: string;
        lastUpdate: string;
        location: string;
    };
}

interface AlertState {
    alerts: Alert[];
    selectedAlertTypeIds: number[];
}

interface AlertActions {
    addAlert: (alert: Alert) => void;
    acknowledgeAlert: (alert: Alert) => void;
    setSelectedAlertTypeIds: (ids: number[]) => void;
}

export type AlertStoreType = AlertState & AlertActions;

const alertTypes = [
    { arabic: 'لا يمكن الاتصال بجهاز التتبع', english: 'Cannot connect to tracker' },
    { arabic: 'تعدى مسافة الرحلة', english: 'Trip distance exceeded' },
    { arabic: 'الشاحنة متوقفه', english: 'Truck stopped' },
    { arabic: 'عكس السير', english: 'Wrong-way driving' },
    { arabic: 'منطقة اشتباه', english: 'Suspicious area' },
    { arabic: 'دخةل منطقة الجمارك', english: 'Entered customs area' },
    { arabic: 'تعديل فترة الإرسال', english: 'Transmission period changed' },
    { arabic: 'فقدان إشارة تحديد الموقع', english: 'Lost GPS signal' },
    { arabic: 'فقدان إشارة شبكة الإتصال', english: 'Lost network signal' },
];

const generateAlerts = (count = 20): Alert[] => {
    const now = new Date();
    const alerts: Alert[] = [];

    for (let i = 0; i < count; i++) {
        const type = alertTypes[Math.floor(Math.random() * alertTypes.length)]; // random alert type
        alerts.push({
            tripId: 10000 + i + 1,
            alertId: 5000 + i + 1,
            timeStamp: new Date(now.getTime() - i * 15 * 60 * 1000).toISOString(),
            alertType: {
                id: alertTypes.indexOf(type) + 1,
                name: type,
            },
            fromStateDateTime: new Date(now.getTime() - (i + 1) * 30 * 60 * 1000).toISOString(),
            toStateDateTime: new Date(now.getTime() - i * 30 * 60 * 1000).toISOString(),
            currentStateOfTrip: {
                id: 300 + i + 1,
                locationSource: 'GPS',
                batteryLevelPercentage: Math.floor(Math.random() * 100),
                chargerStatus: Math.random() > 0.5 ? 1 : 2,
                gpsSignalStrength: Math.floor(Math.random() * 100),
                gsmSignalStrength: Math.floor(Math.random() * 100),
                currentSpeed: Math.floor(Math.random() * 120),
                routeZone: `Route Zone ${i + 1}`,
                isWithinRouteGeofence: Math.random() > 0.2,
                isWithinSuspiciousZone: Math.random() > 0.7,
                remainingDistanceInMeters: Math.floor(Math.random() * 5000),
                timeElapsedSinceTripStartInMinutes: Math.floor(Math.random() * 120),
                trackerDateTime: now.toISOString(),
                dateTime: now.toISOString(),
                tripStatus: 'In Progress',
                lastUpdate: now.toISOString(),
                location: `${29.97 + Math.random().toFixed(4)}° N, ${31.13 + Math.random().toFixed(4)}° E`,
            },
        });
    }

    return alerts;
};

const _alertStore = (instanceId: string) => {
    return (set: any): AlertStoreType => ({
        alerts: generateAlerts(20), // generate 20 dynamic alerts
        selectedAlertTypeIds: [],

        addAlert: (alert: Alert) => set((state: AlertState) => ({ alerts: [...state.alerts, alert] })),
        acknowledgeAlert: (alert: Alert) => {
            set((state: AlertState) => ({
                alerts: state.alerts.filter((a) => a !== alert),
            }));
            logger.info('acknowledgeAlert', { instanceId, alert });
        },

        setSelectedAlertTypeIds: (ids) => set({ selectedAlertTypeIds: ids }),
    });
};

export const useAlertStore = create<AlertStoreType>()(
    subscribeWithSelector(devtools(_alertStore('main'), { name: 'alerts-store' })),
);
