import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import i18n from '@/shared/config/i18n.ts';
import { LocalStorage } from '@/infrastructure/local-storage/local-storage';
import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys';

type LangType = 'en' | 'ar';
type DirType = 'ltr' | 'rtl';

interface LocalizationState {
    currentLanguage: string;
    isRTL: boolean;
    changeLanguage: (language: string) => void;
    initializeLanguage: () => void;
}

export const useLocalizationStore = create<LocalizationState>()(
    devtools(
        (set) => ({
            currentLanguage: i18n.language || 'en',
            isRTL: (i18n.language || 'en') === 'ar',

            changeLanguage: (language: string) => {
                i18n.changeLanguage(language);

                // Save language to localStorage
                LocalStorage.set(LocalStorageKeys.lang, language);

                // Update document direction for RTL support
                if (language === 'ar') {
                    updateLanguageConfig({ dir: 'rtl', lang: 'ar' });
                } else {
                    updateLanguageConfig({ dir: 'ltr', lang: 'en' });
                }

                set({
                    currentLanguage: language,
                    isRTL: language === 'ar',
                });
            },

            initializeLanguage: () => {
                // First try to get language from localStorage
                const storageLang = LocalStorage.get(LocalStorageKeys.lang);
                const currentLang = storageLang || i18n.language || 'en';

                // Change the language if it's different from current
                if (storageLang && storageLang !== i18n.language) {
                    i18n.changeLanguage(storageLang);
                }

                // Set initial document direction
                if (currentLang === 'ar') {
                    updateLanguageConfig({ dir: 'rtl', lang: 'ar' });
                } else {
                    updateLanguageConfig({ dir: 'ltr', lang: 'en' });
                }

                set({
                    currentLanguage: currentLang,
                    isRTL: currentLang === 'ar',
                });
            },
        }),
        {
            name: 'localization-store',
            partialize: (state: LocalizationState) => ({
                currentLanguage: state.currentLanguage,
                isRTL: state.isRTL,
            }),
        },
    ),
);

const updateLanguageConfig = (data: { lang: LangType; dir: DirType }) => {
    document.documentElement.dir = data.dir;
    document.documentElement.lang = data.lang;
};

// Initialize localization state on app start
export const initializeLocalizationStore = () => {
    const store = useLocalizationStore.getState();
    store.initializeLanguage();
};

// Utility hooks for common localization checks
export const useCurrentLanguage = () => useLocalizationStore((state) => state.currentLanguage);
export const useIsRTL = () => useLocalizationStore((state) => state.isRTL);
export const useChangeLanguage = () => useLocalizationStore((state) => state.changeLanguage);
