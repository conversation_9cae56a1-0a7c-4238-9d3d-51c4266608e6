import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';

export interface MyPort {
    alert: { id: number; type: string; details: string };
    transitNumber: number;
    routeName: string;
    shipmentDescription: string;
    timestamp: string;
    address: string;
    trackerSerialNumber: string;
    lastKnownLocation: string;
    driver: {
        id: number;
        name: string;
    };
    truck: {
        id: number;
        plateNumber: string;
        model: string;
        color: string;
        plateCountryName: string;
    };
}

interface MyPortState {
    myPorts: MyPort[];
}

interface MyPortActions {
    addMyPort: (alert: MyPort) => void;
}

export type MyPortStoreType = MyPortState & MyPortActions;

const _alertStore = (instanceId: string) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (set: any): MyPortStoreType => ({
        myPorts: [
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King <PERSON><PERSON><PERSON>tl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
            {
                alert: { id: 10144, type: 'Geofence Entry Breach', details: '1578938' },
                transitNumber: 1,
                routeName: 'King Fahad Intl Airport -Khafji Border',
                shipmentDescription: 'ShipmentDescription',
                timestamp: '27/5/2025 03:00',
                address: '',
                trackerSerialNumber: '1234567890',
                lastKnownLocation: 'الهدف في منفذ الدخول',
                driver: {
                    id: 1,
                    name: 'John Doe',
                },
                truck: {
                    id: 1,
                    plateNumber: '1234567890',
                    model: 'Toyota',
                    color: 'Red',
                    plateCountryName: 'Saudi Arabia',
                },
            },
        ],
        addMyPort: (alert: MyPort) => {
            set((state: MyPortState) => ({ myPorts: [...state.myPorts, alert] }));
            logger.info('addMyPort', { instanceId, alert });
        },
    });
};

export const useMyPortStore = create<MyPortStoreType>()(
    subscribeWithSelector(devtools(_alertStore('main'), { name: 'my-ports-store' })),
);
