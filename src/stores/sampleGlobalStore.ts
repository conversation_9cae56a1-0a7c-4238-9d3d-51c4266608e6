// import { create } from "zustand";
// import { devtools, subscribeWithSelector } from "zustand/middleware";

// export interface Alert {
//     content: string;
//     date: string;
// }

// interface AlertState {
//     alerts: Alert[];
// }

// interface AlertActions {
//     addAlert: (alert: Alert) => void;
//     acknowledgeAlert: (alert: Alert) => void;
// }

// export type AlertStoreType = AlertState & AlertActions;

// const _alertStore = (instanceId: string) => {
//     return (set: any): AlertStoreType => ({
//         alerts: [],
//         addAlert: (alert: Alert) => set((state: AlertState) => ({ alerts: [...state.alerts, alert] })),
//         acknowledgeAlert: (alert: Alert) => set((state: AlertState) => ({ alerts: state.alerts.filter((a) => a !== alert) })),
//     });
// };

// export const useAlertStore = create<AlertStoreType>()(
//       subscribeWithSelector(
//         devtools(_alertStore('main'), { name: 'alert-store' })
//     )
// );
