import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys';
import { logger } from '@/infrastructure/logging';

export interface TripFilterState {
    useFilters: boolean;
    tripCategory: 'myRoutes' | 'suspicious' | 'focused' | 'stopped';
    tripStatus: string[];
}

const DEFAULT_STATE: TripFilterState = {
    useFilters: true,
    tripCategory: 'myRoutes',
    tripStatus: ['onRoute'],
};

type TripFilterActions = {
    setFilters: (filters: TripFilterState) => void;
    setUseFilters: (value: boolean) => void;
    setTripCategory: (category: TripFilterState['tripCategory']) => void;
    toggleTripStatus: (status: string) => void;
};

export type TripFilterStoreType = TripFilterState & TripFilterActions;

//---------------- Store ----------------

const _useTripFilterStore = (instanceId: string) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (set: any, get: () => TripFilterStoreType): TripFilterStoreType => ({
        ...DEFAULT_STATE,
        // TODO: add actions
        setFilters: (filters: TripFilterState) => {
            set({ ...filters });
            logger.info('setFilters', { instanceId, filters });
        },
        setUseFilters: (value) => {
            set({ useFilters: value });
            logger.info('setUseFilters', { instanceId, value });

            // Reset filters if disabled
            if (!value) {
                set({ tripCategory: 'myRoutes', tripStatus: ['onRoute'] });
            }
        },
        setTripCategory: (category) => {
            set({ tripCategory: category });
            logger.info('setTripCategory', { instanceId, category });
        },

        toggleTripStatus: (status) => {
            const { tripStatus } = get();
            const updatedStatus = tripStatus.includes(status)
                ? tripStatus.filter((s: string) => s !== status)
                : [...tripStatus, status];

            set({ tripStatus: updatedStatus });
            logger.info('toggleTripStatus', { instanceId, updatedStatus });
        },
    });
};

export const useTripFilterStore = create<TripFilterStoreType>()(
    subscribeWithSelector(
        persist(devtools(_useTripFilterStore('main'), { name: 'trip-filter' }), {
            name: LocalStorageKeys.tripFilter,
        }),
    ),
);
