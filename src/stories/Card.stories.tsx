import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import {
    <PERSON>,
    CardHeader,
    CardTitle,
    CardDescription,
    CardAction,
    CardContent,
    CardFooter,
} from '@/components/common/ui/Card';

// Mock icons for the stories
const HeartIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
    </svg>
);

const MoreIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <circle cx="12" cy="12" r="1" />
        <circle cx="19" cy="12" r="1" />
        <circle cx="5" cy="12" r="1" />
    </svg>
);

const meta = {
    title: 'Components/Card',
    component: Card,
    parameters: {
        layout: 'centered',
        docs: {
            description: {
                component: `
<div style="max-height: 300px; overflow-y: auto; padding-right: 8px;">
<h1>Card Component</h1>

A flexible card component with header, content, and footer sections. Built with composable sub-components for maximum flexibility.

<h2>Usage in Project</h2>

The Card component is currently used in the following locations:

<h3>1. Monitor Board - Search Tab</h3>
<strong>File:</strong> src/components/features/monitor-board/menu-taps/SearchTap.tsx
- Used for displaying search functionality with charts
- Combines CardHeader, CardTitle, and CardContent
- Contains search fields and line charts

<h3>2. Monitor Board - Alerts Tab</h3>
<strong>File:</strong> src/components/features/monitor-board/menu-taps/AlertsTap.tsx
- Displays new alerts with a "Show All" action
- Uses CardContent, CardHeader, and CardTitle
- Includes navigation links and alert listings

<h3>3. Monitor Board - Routes Tab</h3>
<strong>File:</strong> src/components/features/monitor-board/menu-taps/RoutesTap.tsx
- Simple card with CardHeader and CardTitle only
- Used for route filtering options

<h3>4. My Ports - Map Section</h3>
<strong>File:</strong> src/components/features/my-ports/MapSection.tsx
- Contains Google Maps and charts
- Uses CardContent, CardHeader, and CardTitle
- Integrated with tabs component

<h2>Component Architecture</h2>

The Card component follows a compound component pattern with these sub-components:
<ul>
<li><strong>Card</strong> - Main container</li>
<li><strong>CardHeader</strong> - Header section with grid layout support</li>
<li><strong>CardTitle</strong> - Title text with semantic styling</li>
<li><strong>CardDescription</strong> - Subtitle/description text</li>
<li><strong>CardAction</strong> - Action buttons positioned in header</li>
<li><strong>CardContent</strong> - Main content area</li>
<li><strong>CardFooter</strong> - Footer section for actions</li>
</ul>

<h2>Design System Integration</h2>

- Uses CSS custom properties for theming (bg-card, text-card-foreground)
- Responsive design with container queries
- Consistent spacing and border radius
- Shadow and border styling for depth
</div>
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        className: {
            control: 'text',
            description: 'Additional CSS classes to apply to the card',
        },
    },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic card with just content
export const Default: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px' }}>
            <CardContent>
                <p>This is a simple card with just content. Perfect for basic information display.</p>
            </CardContent>
        </Card>
    ),
};

// Card with header and content
export const WithHeader: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px' }}>
            <CardHeader>
                <CardTitle>Card Title</CardTitle>
                <CardDescription>This is a card description that provides additional context.</CardDescription>
            </CardHeader>
            <CardContent>
                <p>This card includes a header with title and description, plus content area.</p>
            </CardContent>
        </Card>
    ),
};

// Card with header, content, and footer
export const Complete: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px' }}>
            <CardHeader>
                <CardTitle>Complete Card</CardTitle>
                <CardDescription>A fully featured card with all sections.</CardDescription>
            </CardHeader>
            <CardContent>
                <p>This card demonstrates all available sections: header, content, and footer.</p>
                <p>You can put any content here - text, images, forms, or other components.</p>
            </CardContent>
            <CardFooter>
                <button
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    onClick={fn()}>
                    Action Button
                </button>
            </CardFooter>
        </Card>
    ),
};

// Card with action button in header
export const WithHeaderAction: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px' }}>
            <CardHeader>
                <CardTitle>Card with Action</CardTitle>
                <CardDescription>This card has an action button in the header.</CardDescription>
                <CardAction>
                    <button
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                        onClick={fn()}
                        aria-label="More options">
                        <MoreIcon />
                    </button>
                </CardAction>
            </CardHeader>
            <CardContent>
                <p>The action button is positioned in the top-right corner of the card header.</p>
            </CardContent>
        </Card>
    ),
};

// Interactive card example
export const Interactive: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px' }}>
            <CardHeader>
                <CardTitle>Interactive Card</CardTitle>
                <CardDescription>A card with multiple interactive elements.</CardDescription>
                <CardAction>
                    <button
                        className="p-2 hover:bg-red-50 rounded-full transition-colors text-red-500"
                        onClick={fn()}
                        aria-label="Like">
                        <HeartIcon />
                    </button>
                </CardAction>
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                    <p>This card shows how you can combine multiple interactive elements.</p>
                    <div className="flex gap-2">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Tag 1</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">Tag 2</span>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="flex gap-2 w-full">
                    <button
                        className="flex-1 px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                        onClick={fn()}>
                        Cancel
                    </button>
                    <button
                        className="flex-1 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                        onClick={fn()}>
                        Confirm
                    </button>
                </div>
            </CardFooter>
        </Card>
    ),
};

// Card with custom styling
export const CustomStyling: Story = {
    render: (args) => (
        <Card {...args} className="border-2 border-dashed border-purple-300 bg-purple-50" style={{ width: '350px' }}>
            <CardHeader>
                <CardTitle className="text-purple-800">Custom Styled Card</CardTitle>
                <CardDescription className="text-purple-600">
                    This card demonstrates custom styling capabilities.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <p className="text-purple-700">
                    You can easily customize the appearance by passing custom className props to any card component.
                </p>
            </CardContent>
        </Card>
    ),
};

// Minimal card
export const Minimal: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px' }}>
            <CardHeader>
                <CardTitle>Minimal Card</CardTitle>
            </CardHeader>
        </Card>
    ),
};

// Card with long content to show scrolling
export const LongContent: Story = {
    render: (args) => (
        <Card {...args} style={{ width: '350px', maxHeight: '400px' }}>
            <CardHeader>
                <CardTitle>Card with Long Content</CardTitle>
                <CardDescription>This card demonstrates how content flows when it&apos;s longer.</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <p>
                        This is a card with a lot of content to demonstrate how the card handles longer text and
                        multiple elements.
                    </p>
                    <p>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
                        labore et dolore magna aliqua.
                    </p>
                    <p>
                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                        consequat.
                    </p>
                    <p>
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                    </p>
                    <p>
                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim
                        id est laborum.
                    </p>
                </div>
            </CardContent>
            <CardFooter>
                <button
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                    onClick={fn()}>
                    Read More
                </button>
            </CardFooter>
        </Card>
    ),
};

// Real usage examples from the project
export const SearchTabExample: Story = {
    name: 'Real Usage: Search Tab',
    render: (args) => (
        <Card {...args} style={{ width: '400px' }}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="11" cy="11" r="8" />
                        <path d="m21 21-4.35-4.35" />
                    </svg>
                    Search
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <input type="text" placeholder="Search..." className="w-full px-3 py-2 border rounded-md" />
                    <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
                        <span className="text-gray-500">Chart Area</span>
                    </div>
                </div>
            </CardContent>
        </Card>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This example shows how the Card is used in the SearchTap component (src/components/features/monitor-board/menu-taps/SearchTap.tsx). It combines search functionality with data visualization.',
            },
        },
    },
};

export const AlertsTabExample: Story = {
    name: 'Real Usage: Alerts Tab',
    render: (args) => (
        <Card {...args} className="_effect" style={{ width: '400px' }}>
            <CardContent className="grid gap-4">
                <CardHeader className="px-0">
                    <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2">
                                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
                                <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
                            </svg>
                            New Alerts
                        </div>
                        <button className="text-blue-500 text-sm flex items-center gap-1">
                            Show All
                            <svg
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2">
                                <path d="M7 7h10v10" />
                                <path d="M7 17 17 7" />
                            </svg>
                        </button>
                    </CardTitle>
                </CardHeader>
                <div className="space-y-2">
                    <div className="p-2 bg-red-50 border-l-4 border-red-400 rounded">
                        <p className="text-sm font-medium">High Priority Alert</p>
                        <p className="text-xs text-gray-600">2 minutes ago</p>
                    </div>
                    <div className="p-2 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                        <p className="text-sm font-medium">Medium Priority Alert</p>
                        <p className="text-xs text-gray-600">5 minutes ago</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This example replicates the Card usage in AlertsTap component (src/components/features/monitor-board/menu-taps/AlertsTap.tsx). Notice the custom _effect class and the nested CardHeader inside CardContent.',
            },
        },
    },
};

export const MapSectionExample: Story = {
    name: 'Real Usage: Map Section',
    render: (args) => (
        <Card {...args} style={{ width: '400px' }}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M3 6h18l-2 13H5L3 6z" />
                        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                    </svg>
                    Map & Analytics
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div className="h-40 bg-green-100 rounded flex items-center justify-center border-2 border-dashed border-green-300">
                        <span className="text-green-700">Google Map Component</span>
                    </div>
                    <div className="h-24 bg-blue-100 rounded flex items-center justify-center">
                        <span className="text-blue-700">Line Chart Component</span>
                    </div>
                </div>
            </CardContent>
        </Card>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This example shows how the Card is used in MapSection component (src/components/features/my-ports/MapSection.tsx) to contain both Google Maps and chart components.',
            },
        },
    },
};
