import React, { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { Checkbox } from '../components/common/ui/Checkbox';

/**
 * Storybook metadata configuration for Checkbox component.
 *
 * This component is a reusable checkbox with a label,
 * supporting both controlled and uncontrolled usages.
 * It forwards refs and can be styled easily.
 *
 * Common use cases:
 * - Single checkbox toggles
 * - Multiple checkboxes selection lists
 * - Disabled checkboxes to indicate non-interactivity
 */
const meta: Meta<typeof Checkbox> = {
    title: 'Components/Common/UI/Checkbox',
    component: Checkbox,
    tags: ['autodocs'],
    argTypes: {
        label: {
            description: 'Text label displayed next to the checkbox',
            control: 'text',
        },
        checked: {
            description: 'Controls the checked state (for controlled usage)',
            control: 'boolean',
        },
        disabled: {
            description: 'Disables interaction with the checkbox',
            control: 'boolean',
        },
        onChange: {
            description: 'Event handler triggered when checked state changes',
            action: 'changed',
            table: {
                disable: true, // action handles this, no control needed
            },
        },
    },
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

/**
 * Default uncontrolled checkbox example.
 * Useful for simple toggle options where external state management is not required.
 */
export const Default: Story = {
    args: {
        label: 'Accept Terms and Conditions',
    },
};

/**
 * Controlled checkbox example using React local state.
 * Demonstrates how to sync the checked state with an external variable.
 */
export const Controlled = () => {
    const [checked, setChecked] = useState(false);

    return (
        <Checkbox label="Subscribe to newsletter" checked={checked} onChange={(newChecked) => setChecked(newChecked)} />
    );
};
Controlled.storyName = 'Controlled Checkbox';

/**
 * Disabled checkbox example.
 * Shows the visual style and behavior when interaction is disabled.
 */
export const Disabled: Story = {
    args: {
        label: 'I am disabled',
        disabled: true,
    },
};

/**
 * Example of multiple checkbox usage to select multiple options.
 * Demonstrates managing a list of selected items using the Checkbox component.
 */
export const MultipleSelection = () => {
    const items = ['Option 1', 'Option 2', 'Option 3'];
    const [selected, setSelected] = useState<string[]>([]);

    const handleCheckboxChange = (label: string, isChecked: boolean) => {
        setSelected((prev) => (isChecked ? [...prev, label] : prev.filter((item) => item !== label)));
    };

    return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {items.map((item) => (
                <Checkbox
                    key={item}
                    label={item}
                    checked={selected.includes(item)}
                    onChange={(checked) => handleCheckboxChange(item, checked)}
                />
            ))}
            <div style={{ marginTop: 12 }}>
                <strong>Selected:</strong> {selected.join(', ') || 'None'}
            </div>
        </div>
    );
};
MultipleSelection.storyName = 'Multiple Selection';
