import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';

// Import the actual Radio component from your UI library
import { Radio } from '@/components/common/ui/Radio';

const meta = {
    title: 'Components/Radio',
    component: Radio,
    parameters: {
        layout: 'centered',
        docs: {
            description: {
                component: `
<div style="max-height: 300px; overflow-y: auto; padding-right: 8px;">

<h1>Radio Component</h1>

A reusable, styled radio button component with a label. Built with React <strong>forwardRef</strong> for form library integration and automatic ID generation for accessibility.

<h2>Usage in Project</h2>

<h3>1. Filter Button - Sorting Method</h3>
<strong>File:</strong> src/components/common/filter-button/FilterButton.tsx  
- Used for selecting sorting order (ascending/descending)  
- Groups radio buttons with name="sorting-method"  
- Integrated with internationalization (i18next)

<h3>2. Filter Button - Sorting Data</h3>
<strong>File:</strong> src/components/common/filter-button/FilterButton.tsx  
- Used for selecting data field to sort by  
- Multiple options: Trip Code, Transit Number, Entry Port, Exit Port, etc.  
- Groups radio buttons with name="sorting-data"  
- All options use translation keys for multilingual support

<h2>Component Features</h2>
<ul>
<li><strong>Styled with Tailwind CSS</strong> - Consistent visual design</li>
<li><strong>Automatic ID generation</strong> - Uses React.useId() for accessibility</li>
<li><strong>Ref forwarding</strong> - Compatible with form libraries</li>
<li><strong>Hover effects</strong> - Interactive feedback with background changes</li>
<li><strong>Checked state styling</strong> - Blue border when selected</li>
<li><strong>Disabled state support</strong> - Opacity and cursor changes</li>
<li><strong>Custom onChange handler</strong> - Fires only when radio is selected</li>
</ul>

<h2>Design System Integration</h2>
- Uses consistent spacing and typography (text-[14px])  
- Hover states with gray-100 background  
- Blue border (border-blue-300) for checked state  
- Smooth transitions (transition-all duration-200)  
- Proper cursor states (pointer/not-allowed)  
- Flexible layout with gap-3 spacing  
</div>
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        label: {
            control: 'text',
            description: 'The text label displayed next to the radio button',
        },
        name: {
            control: 'text',
            description: 'Name attribute for grouping radio buttons',
        },
        checked: {
            control: 'boolean',
            description: 'Whether the radio button is selected',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the radio button is disabled',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes to apply',
        },
    },
    args: {
        name: 'radio-group',
        checked: false,
        disabled: false,
    },
} satisfies Meta<typeof Radio>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic radio button
export const Default: Story = {
    args: {
        label: 'Default Radio',
        name: 'default-group',
    },
};

export const Checked: Story = {
    args: {
        label: 'Checked Radio',
        name: 'checked-group',
        checked: true,
    },
};

export const Disabled: Story = {
    args: {
        label: 'Disabled Radio',
        name: 'disabled-group',
        disabled: true,
    },
};

export const DisabledChecked: Story = {
    args: {
        label: 'Disabled Checked Radio',
        name: 'disabled-checked-group',
        disabled: true,
        checked: true,
    },
};

// Radio group example component
const RadioGroupExample = () => {
    const [selected, setSelected] = useState<string>('option1');

    const options = [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
    ];

    return (
        <div className="space-y-2">
            <h3 className="text-sm font-medium mb-3">Choose an option:</h3>
            {options.map((option) => (
                <Radio
                    key={option.value}
                    name="example-group"
                    label={option.label}
                    checked={selected === option.value}
                    onChange={() => setSelected(option.value)}
                />
            ))}
            <p className="text-xs text-gray-600 mt-3">Selected: {selected}</p>
        </div>
    );
};

export const RadioGroup: Story = {
    name: 'Radio Group Example',
    args: {
        label: 'Radio Group',
        name: 'example-group',
    },
    render: () => <RadioGroupExample />,
};

// Sorting method example component
const SortingMethodComponent = () => {
    const [sortingMethod, setSortingMethod] = useState<string>('');

    return (
        <div className="space-y-2">
            <h3 className="text-sm font-medium mb-3">Sorting Method:</h3>
            <Radio
                label="Descending"
                name="sorting-method"
                checked={sortingMethod === 'descending'}
                onChange={() => setSortingMethod('descending')}
            />
            <Radio
                label="Ascending"
                name="sorting-method"
                checked={sortingMethod === 'ascending'}
                onChange={() => setSortingMethod('ascending')}
            />
            <p className="text-xs text-gray-600 mt-3">Selected: {sortingMethod || 'None'}</p>
        </div>
    );
};

// Real usage examples from the project
export const SortingMethodExample: Story = {
    name: 'Real Usage: Sorting Method',
    args: {
        label: 'Sorting Method',
        name: 'sorting-method',
    },
    render: () => <SortingMethodComponent />,
    parameters: {
        docs: {
            description: {
                story: 'This example shows how Radio buttons are used in FilterButton component (src/components/common/filter-button/FilterButton.tsx) for selecting sorting order.',
            },
        },
    },
};

// Sorting data example component
const SortingDataComponent = () => {
    const [sortingData, setSortingData] = useState<string>('');

    const dataFields = [
        { value: 'tripCode', label: 'Trip Code' },
        { value: 'transitNumber', label: 'Transit Number' },
        { value: 'entryPort', label: 'Entry Port' },
        { value: 'exitPort', label: 'Exit Port' },
        { value: 'transitDate', label: 'Transit Date' },
        { value: 'entryDate', label: 'Entry Date' },
        { value: 'exitDate', label: 'Exit Date' },
        { value: 'createdDate', label: 'Created Date' },
    ];

    return (
        <div className="space-y-2 max-w-xs">
            <h3 className="text-sm font-medium mb-3">Sort by:</h3>
            {dataFields.map((field) => (
                <Radio
                    key={field.value}
                    label={field.label}
                    name="sorting-data"
                    checked={sortingData === field.value}
                    onChange={() => setSortingData(field.value)}
                />
            ))}
            <p className="text-xs text-gray-600 mt-3">Selected: {sortingData || 'None'}</p>
        </div>
    );
};

export const SortingDataExample: Story = {
    name: 'Real Usage: Sorting Data Fields',
    args: {
        label: 'Sorting Data',
        name: 'sorting-data',
    },
    render: () => <SortingDataComponent />,
    parameters: {
        docs: {
            description: {
                story: 'This example replicates the Radio usage in FilterButton component for selecting which data field to sort by. All options are grouped under name="sorting-data".',
            },
        },
    },
};

// Custom styling example
export const CustomStyling: Story = {
    args: {
        label: 'Custom Styled Radio',
        name: 'custom-group',
        className: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
    },
};

// Long label example
export const LongLabel: Story = {
    args: {
        label: 'This is a radio button with a very long label that demonstrates how the component handles text wrapping and maintains proper alignment',
        name: 'long-label-group',
    },
};
